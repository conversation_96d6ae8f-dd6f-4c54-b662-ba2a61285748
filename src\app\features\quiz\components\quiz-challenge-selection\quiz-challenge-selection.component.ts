import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';
import { QuizChallenge } from '../../models/quiz.models';

@Component({
  selector: 'app-quiz-challenge-selection',
  templateUrl: './quiz-challenge-selection.component.html',
  styleUrls: ['./quiz-challenge-selection.component.css']
})
export class QuizChallengeSelectionComponent implements OnInit {
  challenges: QuizChallenge[] = [];
  filteredChallenges: QuizChallenge[] = [];
  loading = true;
  errorMessage: string | null = null;
  currentUser: any;

  // Filtres
  selectedCategory: string = 'all';
  selectedDifficulty: string = 'all';
  searchTerm: string = '';

  // Catégories uniques pour le filtre
  categories: { id: string; name: string }[] = [];

  constructor(
    private quizService: QuizService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadChallenges();
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    } else {
      this.errorMessage = 'Utilisateur non connecté.';
      this.router.navigate(['/login']);
    }
  }

  loadChallenges(): void {
    this.loading = true;
    this.quizService.getAllChallenges().subscribe({
      next: (challenges) => {
        this.challenges = challenges;
        this.extractCategories();
        this.applyFilters();
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des challenges:', error);
        this.errorMessage = 'Impossible de charger les challenges.';
        this.loading = false;
      }
    });
  }

  extractCategories(): void {
    const categoryMap = new Map();
    this.challenges.forEach(challenge => {
      if (challenge.category && challenge.category._id) {
        categoryMap.set(challenge.category._id, {
          id: challenge.category._id,
          name: challenge.category.name
        });
      }
    });
    this.categories = Array.from(categoryMap.values());
  }

  applyFilters(): void {
    this.filteredChallenges = this.challenges.filter(challenge => {
      const matchesCategory = this.selectedCategory === 'all' ||
        (challenge.category && challenge.category._id === this.selectedCategory);

      const matchesDifficulty = this.selectedDifficulty === 'all' ||
        challenge.difficulty === this.selectedDifficulty;

      const matchesSearch = challenge.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        challenge.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        (challenge.categoryName && challenge.categoryName.toLowerCase().includes(this.searchTerm.toLowerCase()));

      return matchesCategory && matchesDifficulty && matchesSearch && challenge.isActive;
    });
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  startQuiz(challenge: QuizChallenge): void {
    if (!challenge.isActive) {
      this.errorMessage = 'Ce challenge n\'est pas disponible actuellement.';
      return;
    }

    if (challenge.totalQuestions === 0) {
      this.errorMessage = 'Ce challenge ne contient aucune question.';
      return;
    }

    // Sauvegarder le challenge sélectionné
    localStorage.setItem('selectedChallenge', JSON.stringify(challenge));

    // Naviguer vers le composant de quiz
    this.router.navigate(['/quiz/play', challenge._id]);
  }

  playQuiz(challenge: QuizChallenge): void {
    console.log('Jouer au quiz:', challenge);
    this.router.navigate(['/quiz/play', challenge._id]);
  }

  getDifficultyColor(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }

  getDifficultyBadgeClass(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getDifficultyIcon(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return '🟢';
      case 'medium': return '🟡';
      case 'hard': return '🔴';
      default: return '⚪';
    }
  }

  getChallengeIcon(title: string): string {
    const titleLower = title.toLowerCase();
    if (titleLower.includes('javascript')) return '🟨';
    if (titleLower.includes('python')) return '🐍';
    if (titleLower.includes('java')) return '☕';
    if (titleLower.includes('angular')) return '🅰️';
    if (titleLower.includes('react')) return '⚛️';
    if (titleLower.includes('node')) return '🟢';
    if (titleLower.includes('database') || titleLower.includes('sql')) return '🗄️';
    if (titleLower.includes('security')) return '🔒';
    if (titleLower.includes('mobile')) return '📱';
    if (titleLower.includes('web')) return '🌐';
    return '🧠';
  }

  getEstimatedTime(totalQuestions: number): string {
    const minutesPerQuestion = 1.5;
    const totalMinutes = Math.ceil(totalQuestions * minutesPerQuestion);
    return `${totalMinutes} min`;
  }

  clearFilters(): void {
    this.selectedCategory = 'all';
    this.selectedDifficulty = 'all';
    this.searchTerm = '';
    this.applyFilters();
  }

  refreshChallenges(): void {
    this.loadChallenges();
  }

  goToProfile(): void {
    this.router.navigate(['/quiz/quiz-profile']);
  }

  goToLeaderboard(): void {
    this.router.navigate(['/quiz/leaderboard']);
  }

  goToCategories(): void {
    this.router.navigate(['/quiz/categories']);
  }
}
