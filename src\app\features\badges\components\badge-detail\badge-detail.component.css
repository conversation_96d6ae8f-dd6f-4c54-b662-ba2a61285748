.badge-details-container {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #ffffff;
  }
  
  .badge-details-title {
    color: #8e44ad;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
  }
  
  /* Carte de détails sélectionnés */
  .selected-badge-card {
    background-color: #2c2c54;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    transition: transform 0.3s ease;
  }
  
  .selected-badge-card:hover {
    transform: scale(1.02);
  }
  
  /* Image badge */
  .badge-image {
    max-width: 120px;
    border-radius: 10px;
    border: 2px solid #8e44ad;
    box-shadow: 0 4px 12px rgba(142, 68, 173, 0.4);
  }
  
  /* Liste des badges */
  .badge-list {
    border-radius: 10px;
    background-color: #3b3b6d;
    overflow: hidden;
  }
  
  .badge-list-item {
    background-color: #4a4a8a;
    color: #f5f5f5;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .badge-list-item:hover {
    background-color: #8e44ad;
    color: #ffffff;
    font-weight: bold;
  }
  