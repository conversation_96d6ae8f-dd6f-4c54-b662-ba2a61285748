/* Container style */
.badge-form-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
  
  /* Card style */
  .badge-form-card {
    border: none;
    border-radius: 15px;
    background: #2c2c54;
    color: #ffffff;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
    transform: perspective(1000px) translateZ(0);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  .badge-form-card:hover {
    transform: perspective(1000px) translateZ(10px);
    box-shadow: 0 15px 35px rgba(142, 68, 173, 0.6);
  }
  
  /* Header with violet dark color */
  .badge-form-header {
    background-color: #8e44ad;
    color: #fff;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
  }
  
  /* Input styles */
  .badge-input {
    border-radius: 8px;
    border: 1px solid #9b59b6;
    background-color: #fdfdff;
    color: #2c3e50;
    transition: all 0.3s ease-in-out;
  }
  
  .badge-input:focus {
    border-color: #8e44ad;
    box-shadow: 0 0 5px #9b59b6;
  }
  
  /* Submit button */
  .badge-submit-btn {
    background-color: #8e44ad;
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(142, 68, 173, 0.4);
  }
  
  .badge-submit-btn:hover {
    background-color: #732d91;
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(142, 68, 173, 0.6);
  }
  