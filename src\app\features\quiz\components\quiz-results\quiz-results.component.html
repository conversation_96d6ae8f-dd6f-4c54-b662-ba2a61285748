<div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
  <div class="max-w-6xl mx-auto">
    
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">📊 Résultats Détaillés</h1>
      <p class="text-xl text-gray-300">{{ challenge?.title }}</p>
    </div>

    <div *ngIf="result && challenge" class="space-y-8">
      
      <!-- Score principal -->
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-8 text-center">
        <div class="text-8xl mb-4">{{ scoreIcon }}</div>
        <div [class]="scoreColor" class="text-6xl font-bold mb-4">{{ result.percentage }}%</div>
        <p class="text-2xl text-white mb-4">{{ result.correctAnswers.length }} / {{ result.totalQuestions }} bonnes réponses</p>
        <p class="text-lg text-gray-300 mb-6">{{ performanceMessage }}</p>
        
        <!-- Points gagnés -->
        <div class="inline-flex items-center px-6 py-3 bg-yellow-600 text-white rounded-lg text-xl font-semibold">
          ⭐ {{ result.score }} points gagnés
        </div>
      </div>

      <!-- Statistiques détaillées -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
          <div class="text-3xl mb-2">⏱️</div>
          <div class="text-2xl font-bold text-white">{{ timeSpentFormatted }}</div>
          <div class="text-gray-300">Temps total</div>
        </div>
        
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
          <div class="text-3xl mb-2">⚡</div>
          <div class="text-2xl font-bold text-white">{{ averageTimePerQuestion }}</div>
          <div class="text-gray-300">Temps moyen/question</div>
        </div>
        
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
          <div class="text-3xl mb-2">🎯</div>
          <div class="text-2xl font-bold text-green-400">{{ result.correctAnswers.length }}</div>
          <div class="text-gray-300">Bonnes réponses</div>
        </div>
        
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
          <div class="text-3xl mb-2">❌</div>
          <div class="text-2xl font-bold text-red-400">{{ result.incorrectAnswers.length }}</div>
          <div class="text-gray-300">Erreurs</div>
        </div>
      </div>

      <!-- Badge gagné -->
      <div *ngIf="result.badgeEarned" class="bg-gradient-to-r {{ badgeTypeColor }} rounded-xl p-6">
        <div class="text-center">
          <div class="text-5xl mb-4">🏆</div>
          <h3 class="text-2xl font-bold text-white mb-2">Nouveau Badge Gagné !</h3>
          <p class="text-xl text-white mb-2">{{ result.badgeEarned.name }}</p>
          <p class="text-lg text-white/80">Type: {{ result.badgeEarned.type }}</p>
        </div>
      </div>

      <!-- Certificat gagné -->
      <div *ngIf="result.certificateEarned" class="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl p-8">
        <div class="text-center">
          <div class="text-6xl mb-4">🎓</div>
          <h3 class="text-3xl font-bold text-white mb-4">🎉 FÉLICITATIONS ! 🎉</h3>
          <p class="text-xl text-white mb-4">Vous avez atteint 1000 points et gagné votre certificat !</p>
          <p class="text-lg text-white/90 mb-6">Score total: {{ result.newTotalScore }} points</p>
          <button 
            (click)="downloadCertificate()"
            class="px-6 py-3 bg-white text-orange-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            📜 Télécharger le Certificat
          </button>
        </div>
      </div>

      <!-- Analyse des réponses -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- Bonnes réponses -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-green-400">✅ Bonnes Réponses ({{ result.correctAnswers.length }})</h3>
            <button 
              (click)="toggleCorrectAnswers()"
              class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
              {{ showCorrectAnswers ? 'Masquer' : 'Afficher' }}
            </button>
          </div>
          
          <div *ngIf="showCorrectAnswers" class="space-y-4 max-h-96 overflow-y-auto">
            <div *ngFor="let question of result.correctAnswers; let i = index" 
                 class="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
              <div class="flex items-center mb-2">
                <span class="px-2 py-1 bg-green-600 text-white rounded text-sm mr-2">Q{{ i + 1 }}</span>
                <span [class]="getDifficultyColor(getQuestionDifficulty(question))" class="text-sm">
                  {{ getDifficultyIcon(getQuestionDifficulty(question)) }} {{ getQuestionDifficulty(question) | titlecase }}
                </span>
              </div>
              <p class="text-white text-sm mb-2" [innerHTML]="question.question"></p>
              <p class="text-green-300 text-sm">
                <strong>Réponse correcte:</strong> <span [innerHTML]="question.correct_answer"></span>
              </p>
            </div>
          </div>
        </div>

        <!-- Mauvaises réponses -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-bold text-red-400">❌ Réponses Incorrectes ({{ result.incorrectAnswers.length }})</h3>
            <button 
              (click)="toggleIncorrectAnswers()"
              class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
              {{ showIncorrectAnswers ? 'Masquer' : 'Afficher' }}
            </button>
          </div>
          
          <div *ngIf="showIncorrectAnswers" class="space-y-4 max-h-96 overflow-y-auto">
            <div *ngFor="let question of result.incorrectAnswers; let i = index" 
                 class="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
              <div class="flex items-center mb-2">
                <span class="px-2 py-1 bg-red-600 text-white rounded text-sm mr-2">Q{{ i + 1 }}</span>
                <span [class]="getDifficultyColor(getQuestionDifficulty(question))" class="text-sm">
                  {{ getDifficultyIcon(getQuestionDifficulty(question)) }} {{ getQuestionDifficulty(question) | titlecase }}
                </span>
              </div>
              <p class="text-white text-sm mb-2" [innerHTML]="question.question"></p>
              <p class="text-red-300 text-sm mb-1">
                <strong>Votre réponse:</strong> <span [innerHTML]="question.selectedAnswer || 'Non répondu'"></span>
              </p>
              <p class="text-green-300 text-sm">
                <strong>Réponse correcte:</strong> <span [innerHTML]="question.correct_answer"></span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
        <h3 class="text-xl font-bold text-white mb-6 text-center">Que souhaitez-vous faire maintenant ?</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button 
            (click)="retakeQuiz()"
            class="flex flex-col items-center p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <div class="text-2xl mb-2">🔄</div>
            <span class="font-semibold">Refaire ce quiz</span>
          </button>
          
          <button 
            (click)="goToCategoryQuizzes()"
            class="flex flex-col items-center p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <div class="text-2xl mb-2">📚</div>
            <span class="font-semibold">Autres quiz</span>
          </button>
          
          <button 
            (click)="shareResults()"
            class="flex flex-col items-center p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            <div class="text-2xl mb-2">📤</div>
            <span class="font-semibold">Partager</span>
          </button>
          
          <button 
            (click)="goToCategories()"
            class="flex flex-col items-center p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            <div class="text-2xl mb-2">🏠</div>
            <span class="font-semibold">Accueil</span>
          </button>
        </div>
      </div>

      <!-- Conseils pour améliorer -->
      <div *ngIf="result.percentage < 80" class="bg-white/10 backdrop-blur-md rounded-xl p-6">
        <h3 class="text-xl font-bold text-yellow-400 mb-4">💡 Conseils pour s'améliorer</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-white/10 rounded-lg p-4">
            <h4 class="font-semibold text-white mb-2">📖 Révision</h4>
            <p class="text-gray-300 text-sm">Concentrez-vous sur les sujets où vous avez fait des erreurs.</p>
          </div>
          <div class="bg-white/10 rounded-lg p-4">
            <h4 class="font-semibold text-white mb-2">🎯 Pratique</h4>
            <p class="text-gray-300 text-sm">Refaites ce quiz ou essayez d'autres quiz similaires.</p>
          </div>
          <div class="bg-white/10 rounded-lg p-4">
            <h4 class="font-semibold text-white mb-2">⏰ Gestion du temps</h4>
            <p class="text-gray-300 text-sm">Prenez votre temps pour bien lire chaque question.</p>
          </div>
          <div class="bg-white/10 rounded-lg p-4">
            <h4 class="font-semibold text-white mb-2">🤝 Communauté</h4>
            <p class="text-gray-300 text-sm">Discutez avec d'autres utilisateurs pour échanger des conseils.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Message si pas de données -->
    <div *ngIf="!result || !challenge" class="text-center text-white">
      <div class="text-6xl mb-4">❓</div>
      <h2 class="text-2xl font-bold mb-4">Aucun résultat à afficher</h2>
      <p class="text-gray-300 mb-6">Les données du quiz n'ont pas pu être récupérées.</p>
      <button 
        (click)="goToCategories()"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        Retour aux catégories
      </button>
    </div>
  </div>
</div>
