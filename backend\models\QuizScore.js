const mongoose = require('mongoose');

const QuizScoreSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  challengeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Challenge',
    required: true
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  score: {
    type: Number,
    required: true,
    min: 0
  },
  totalQuestions: {
    type: Number,
    required: true,
    min: 1
  },
  percentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  timeSpent: {
    type: Number, // en secondes
    default: 0
  },
  answers: {
    type: Map,
    of: String, // questionId -> selectedAnswer
    default: new Map()
  },
  correctAnswers: [{
    questionId: String,
    question: String,
    correctAnswer: String,
    userAnswer: String
  }],
  incorrectAnswers: [{
    questionId: String,
    question: String,
    correctAnswer: String,
    userAnswer: String
  }],
  completedAt: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index pour optimiser les requêtes
QuizScoreSchema.index({ userId: 1, completedAt: -1 });
QuizScoreSchema.index({ categoryId: 1, percentage: -1 });
QuizScoreSchema.index({ challengeId: 1 });

// Middleware pour mettre à jour updatedAt
QuizScoreSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Méthodes statiques
QuizScoreSchema.statics.getUserTotalScore = async function(userId) {
  const result = await this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalScore: { $sum: '$score' },
        totalQuizzes: { $sum: 1 },
        averagePercentage: { $avg: '$percentage' }
      }
    }
  ]);
  
  return result[0] || { totalScore: 0, totalQuizzes: 0, averagePercentage: 0 };
};

QuizScoreSchema.statics.getLeaderboard = async function(limit = 10) {
  return await this.aggregate([
    {
      $group: {
        _id: '$userId',
        totalScore: { $sum: '$score' },
        totalQuizzes: { $sum: 1 },
        averageScore: { $avg: '$percentage' },
        lastQuizDate: { $max: '$completedAt' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $lookup: {
        from: 'badges',
        localField: '_id',
        foreignField: 'userId',
        as: 'badges'
      }
    },
    {
      $addFields: {
        userName: '$user.fullName',
        highestBadge: {
          $arrayElemAt: [
            {
              $map: {
                input: { $slice: [{ $sortArray: { input: '$badges', sortBy: { awardedAt: -1 } } }, 1] },
                as: 'badge',
                in: '$$badge.type'
              }
            },
            0
          ]
        }
      }
    },
    {
      $sort: { totalScore: -1 }
    },
    {
      $limit: limit
    },
    {
      $project: {
        userId: '$_id',
        userName: 1,
        totalScore: 1,
        totalQuizzes: 1,
        averageScore: { $round: ['$averageScore', 1] },
        highestBadge: { $ifNull: ['$highestBadge', 'PARTICIPANT'] },
        lastQuizDate: 1
      }
    }
  ]);
};

QuizScoreSchema.statics.getCategoryLeaderboard = async function(categoryId, limit = 10) {
  return await this.aggregate([
    { $match: { categoryId: mongoose.Types.ObjectId(categoryId) } },
    {
      $group: {
        _id: '$userId',
        totalScore: { $sum: '$score' },
        totalQuizzes: { $sum: 1 },
        averageScore: { $avg: '$percentage' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $addFields: {
        userName: '$user.fullName'
      }
    },
    {
      $sort: { totalScore: -1 }
    },
    {
      $limit: limit
    },
    {
      $project: {
        userId: '$_id',
        userName: 1,
        totalScore: 1,
        totalQuizzes: 1,
        averageScore: { $round: ['$averageScore', 1] }
      }
    }
  ]);
};

module.exports = mongoose.model('QuizScore', QuizScoreSchema);
