<div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">
        🏆 Mes Badges & Certificats
      </h1>
      <p class="text-xl text-gray-300 mb-6">
        Découvrez vos accomplissements et téléchargez vos certificats
      </p>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded-lg mb-6">
      {{ errorMessage }}
      <button (click)="errorMessage = null" class="float-right text-red-100 hover:text-white">×</button>
    </div>

    <!-- Statistiques -->
    <div *ngIf="badgeStats" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-3xl font-bold text-white">{{ badgeStats.totalBadges }}</div>
        <div class="text-gray-300">Badges totaux</div>
      </div>
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-3xl font-bold text-green-400">{{ badgeStats.totalScore }}</div>
        <div class="text-gray-300">Points totaux</div>
      </div>
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-3xl font-bold text-blue-400">{{ badgeStats.averagePercentage | number:'1.1-1' }}%</div>
        <div class="text-gray-300">Moyenne</div>
      </div>
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-3xl font-bold text-yellow-400">{{ badgeStats.certificatesEarned }}</div>
        <div class="text-gray-300">Certificats</div>
      </div>
    </div>

    <!-- Certificats -->
    <div *ngIf="certificateBadges.length > 0" class="mb-8">
      <h2 class="text-2xl font-bold text-white mb-4">🎓 Mes Certificats</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let certificate of certificateBadges" 
             class="bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition-all">
          <div class="text-center mb-4">
            <img [src]="certificate.certificateImageUrl" 
                 [alt]="'Certificat ' + certificate.name"
                 class="w-full h-48 object-contain rounded-lg bg-white/5">
          </div>
          <h3 class="text-lg font-bold text-white mb-2">{{ certificate.name }}</h3>
          <p class="text-gray-300 text-sm mb-4">{{ certificate.description }}</p>
          <div class="flex space-x-2">
            <button (click)="downloadCertificate(certificate)"
                    class="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              📥 Télécharger
            </button>
            <button (click)="shareBadge(certificate)"
                    class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              📤 Partager
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div *ngIf="loading" class="text-center text-white">
      <div class="text-6xl mb-4">⏳</div>
      <h3 class="text-2xl font-bold mb-4">Chargement de vos badges...</h3>
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
    </div>

    <!-- Liste des badges -->
    <div *ngIf="!loading && getFilteredBadges().length > 0">
      <h2 class="text-2xl font-bold text-white mb-4">🏅 Mes Badges</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div *ngFor="let badge of getFilteredBadges()" 
             class="bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition-all">
          
          <!-- Badge image -->
          <div class="text-center mb-4">
            <img *ngIf="badge.imageUrl" 
                 [src]="badge.imageUrl" 
                 [alt]="badge.name"
                 class="w-32 h-32 mx-auto object-contain">
            <div *ngIf="!badge.imageUrl" 
                 class="w-32 h-32 mx-auto flex items-center justify-center text-6xl"
                 [style.color]="getBadgeTypeColor(badge.type)">
              {{ getBadgeTypeIcon(badge.type) }}
            </div>
          </div>

          <!-- Badge info -->
          <div class="text-center">
            <h3 class="text-xl font-bold text-white mb-2">{{ badge.name }}</h3>
            <p class="text-gray-300 text-sm mb-3">{{ badge.description }}</p>
            
            <!-- Type et score -->
            <div class="flex justify-center space-x-2 mb-3">
              <span class="px-2 py-1 rounded-full text-xs font-semibold"
                    [style.background-color]="getBadgeTypeColor(badge.type)"
                    [style.color]="'white'">
                {{ getBadgeTypeName(badge.type) }}
              </span>
              <span [class]="getScoreBadgeClass(badge.percentage)"
                    class="px-2 py-1 rounded-full text-xs font-semibold">
                {{ badge.percentage }}%
              </span>
            </div>

            <!-- Catégorie et date -->
            <div class="text-sm text-gray-400 mb-4">
              <div>📂 {{ badge.category?.name || 'Catégorie inconnue' }}</div>
              <div>📅 {{ formatDate(badge.awardedAt) }}</div>
              <div *ngIf="badge.quizScore > 0">🎯 {{ badge.quizScore }} points</div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
              <button (click)="shareBadge(badge)"
                      class="flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                📤 Partager
              </button>
              <button *ngIf="badge.certificateImageUrl" 
                      (click)="downloadCertificate(badge)"
                      class="flex-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
                📜 Certificat
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message si aucun badge -->
    <div *ngIf="!loading && getFilteredBadges().length === 0" class="text-center text-white">
      <div class="text-6xl mb-4">🏆</div>
      <h3 class="text-2xl font-bold mb-4">
        <span *ngIf="badges.length === 0">Aucun badge obtenu</span>
        <span *ngIf="badges.length > 0">Aucun badge ne correspond aux filtres</span>
      </h3>
      <p class="text-gray-300 mb-6">
        <span *ngIf="badges.length === 0">Complétez des quiz pour gagner vos premiers badges !</span>
        <span *ngIf="badges.length > 0">Essayez de modifier vos critères de recherche.</span>
      </p>
      <div class="space-x-4">
        <button *ngIf="badges.length > 0" (click)="clearFilters()"
                class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Effacer les filtres
        </button>
        <button (click)="refreshBadges()"
                class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          Actualiser
        </button>
      </div>
    </div>
  </div>
</div>
