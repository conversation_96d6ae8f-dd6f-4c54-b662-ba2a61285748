<div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-6">
  <div class="max-w-6xl mx-auto">
    
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">🏆 Classement des Quiz</h1>
      <p class="text-xl text-gray-300">Découvrez les meilleurs joueurs de SkillsHub !</p>
    </div>

    <!-- Actions -->
    <div class="flex flex-wrap gap-4 justify-center mb-8">
      <button 
        (click)="goToProfile()"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        👤 Mon Profil
      </button>
      <button 
        (click)="goToQuizzes()"
        class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
        🎯 Faire un Quiz
      </button>
      <button 
        (click)="refreshLeaderboard()"
        class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
        🔄 Actualiser
      </button>
      <button 
        (click)="shareLeaderboard()"
        class="px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
        📤 Partager
      </button>
    </div>

    <!-- Sélecteur de catégorie -->
    <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 mb-8">
      <h3 class="text-xl font-bold text-white mb-4">📊 Choisir un classement</h3>
      <div class="flex flex-wrap gap-2">
        <button 
          (click)="selectCategory('global')"
          [class]="selectedCategory === 'global' ? 'bg-blue-600 text-white' : 'bg-white/20 text-gray-300 hover:bg-white/30'"
          class="px-4 py-2 rounded-lg transition-colors">
          🌍 Global
        </button>
        <button 
          *ngFor="let category of categories"
          (click)="selectCategory(category._id)"
          [class]="selectedCategory === category._id ? 'bg-blue-600 text-white' : 'bg-white/20 text-gray-300 hover:bg-white/30'"
          class="px-4 py-2 rounded-lg transition-colors">
          {{ category.name }}
        </button>
      </div>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-500 text-white p-4 rounded-lg mb-6 text-center">
      {{ errorMessage }}
    </div>

    <!-- Loading -->
    <div *ngIf="loading" class="text-center">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      <p class="text-white mt-4">Chargement du classement...</p>
    </div>

    <!-- Position de l'utilisateur actuel -->
    <div *ngIf="!loading && currentUser && currentUserRank > 0" class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 mb-8">
      <div class="text-center">
        <h3 class="text-xl font-bold text-white mb-2">🎯 Votre Position</h3>
        <div class="text-3xl font-bold text-white mb-2">{{ getRankIcon(currentUserRank) }} {{ currentUserRank }}</div>
        <p class="text-white/80">
          Classement {{ selectedCategory === 'global' ? 'global' : getCategoryName(selectedCategory) }}
        </p>
      </div>
    </div>

    <!-- Podium (Top 3) -->
    <div *ngIf="!loading && currentLeaderboard.length >= 3" class="mb-8">
      <h3 class="text-2xl font-bold text-white text-center mb-6">🏆 Podium</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        <!-- 2ème place -->
        <div class="bg-gradient-to-br from-gray-400 to-gray-600 rounded-xl p-6 text-center order-2 md:order-1">
          <div class="text-4xl mb-3">🥈</div>
          <div class="text-xl font-bold text-white mb-2">{{ currentLeaderboard[1]?.userName || 'Utilisateur' }}</div>
          <div class="text-2xl font-bold text-white mb-2">{{ formatScore(currentLeaderboard[1]?.totalScore || 0) }}</div>
          <div class="text-sm text-gray-200">{{ currentLeaderboard[1]?.totalQuizzes || 0 }} quiz</div>
          <div class="mt-3">
            <span class="px-3 py-1 bg-white/20 rounded-full text-sm">
              {{ getBadgeIcon(currentLeaderboard[1]?.highestBadge) }} {{ currentLeaderboard[1]?.highestBadge }}
            </span>
          </div>
        </div>

        <!-- 1ère place -->
        <div class="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl p-8 text-center order-1 md:order-2 transform md:scale-110">
          <div class="text-6xl mb-4">🥇</div>
          <div class="text-2xl font-bold text-white mb-3">{{ currentLeaderboard[0]?.userName || 'Utilisateur' }}</div>
          <div class="text-3xl font-bold text-white mb-3">{{ formatScore(currentLeaderboard[0]?.totalScore || 0) }}</div>
          <div class="text-sm text-white/80 mb-4">{{ currentLeaderboard[0]?.totalQuizzes || 0 }} quiz complétés</div>
          <div class="mt-4">
            <span class="px-4 py-2 bg-white/20 rounded-full text-sm font-semibold">
              {{ getBadgeIcon(currentLeaderboard[0]?.highestBadge) }} {{ currentLeaderboard[0]?.highestBadge }}
            </span>
          </div>
        </div>

        <!-- 3ème place -->
        <div class="bg-gradient-to-br from-orange-400 to-red-500 rounded-xl p-6 text-center order-3">
          <div class="text-4xl mb-3">🥉</div>
          <div class="text-xl font-bold text-white mb-2">{{ currentLeaderboard[2]?.userName || 'Utilisateur' }}</div>
          <div class="text-2xl font-bold text-white mb-2">{{ formatScore(currentLeaderboard[2]?.totalScore || 0) }}</div>
          <div class="text-sm text-gray-200">{{ currentLeaderboard[2]?.totalQuizzes || 0 }} quiz</div>
          <div class="mt-3">
            <span class="px-3 py-1 bg-white/20 rounded-full text-sm">
              {{ getBadgeIcon(currentLeaderboard[2]?.highestBadge) }} {{ currentLeaderboard[2]?.highestBadge }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Classement complet -->
    <div *ngIf="!loading && currentLeaderboard.length > 0" class="bg-white/10 backdrop-blur-md rounded-xl p-6">
      <h3 class="text-xl font-bold text-white mb-6">
        📋 Classement {{ selectedCategory === 'global' ? 'Global' : getCategoryName(selectedCategory) }}
      </h3>
      
      <div class="space-y-3">
        <div 
          *ngFor="let user of currentLeaderboard; let i = index"
          class="flex items-center justify-between p-4 rounded-lg transition-all duration-200"
          [class]="isCurrentUser(user.userId) ? 
            'bg-blue-600/30 border-2 border-blue-500' : 
            'bg-white/10 hover:bg-white/20'">
          
          <div class="flex items-center space-x-4">
            <!-- Rang -->
            <div class="w-12 text-center">
              <span [class]="getRankColor(i + 1)" class="text-xl font-bold">
                {{ getRankIcon(i + 1) }}
              </span>
            </div>
            
            <!-- Avatar et nom -->
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                {{ (user.userName || 'U').charAt(0).toUpperCase() }}
              </div>
              <div>
                <div class="text-white font-semibold">
                  {{ user.userName || 'Utilisateur' }}
                  <span *ngIf="isCurrentUser(user.userId)" class="text-blue-400 text-sm ml-2">(Vous)</span>
                </div>
                <div class="text-gray-300 text-sm">{{ user.totalQuizzes || 0 }} quiz complétés</div>
              </div>
            </div>
          </div>

          <!-- Score et badge -->
          <div class="text-right">
            <div class="text-xl font-bold text-white mb-1">{{ formatScore(user.totalScore || 0) }}</div>
            <div class="flex items-center justify-end space-x-2">
              <span class="text-gray-300 text-sm">{{ user.averageScore || 0 }}% moy.</span>
              <span class="px-2 py-1 bg-white/20 rounded-full text-xs">
                {{ getBadgeIcon(user.highestBadge) }}
              </span>
            </div>
            
            <!-- Barre de progression -->
            <div class="w-24 bg-gray-700 rounded-full h-2 mt-2">
              <div 
                class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                [style.width.%]="getProgressToNextLevel(user.totalScore || 0)">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message si pas de données -->
    <div *ngIf="!loading && currentLeaderboard.length === 0" class="text-center text-white py-12">
      <div class="text-6xl mb-4">🏆</div>
      <h3 class="text-2xl font-bold mb-4">Aucun classement disponible</h3>
      <p class="text-gray-300 mb-6">
        Soyez le premier à apparaître dans ce classement !
      </p>
      <button 
        (click)="goToQuizzes()"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        🎯 Commencer un quiz
      </button>
    </div>

    <!-- Informations sur le système de points -->
    <div class="mt-12 bg-white/10 backdrop-blur-md rounded-xl p-6">
      <h3 class="text-xl font-bold text-white mb-4 text-center">ℹ️ Comment fonctionne le classement ?</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">📊</div>
          <h4 class="text-white font-semibold mb-2">Score Total</h4>
          <p class="text-gray-300 text-sm">Somme de tous vos points gagnés dans les quiz</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">🎯</div>
          <h4 class="text-white font-semibold mb-2">Nombre de Quiz</h4>
          <p class="text-gray-300 text-sm">Total des quiz que vous avez complétés</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">🏅</div>
          <h4 class="text-white font-semibold mb-2">Badge le Plus Élevé</h4>
          <p class="text-gray-300 text-sm">Votre meilleur niveau de badge obtenu</p>
        </div>
      </div>
    </div>
  </div>
</div>
