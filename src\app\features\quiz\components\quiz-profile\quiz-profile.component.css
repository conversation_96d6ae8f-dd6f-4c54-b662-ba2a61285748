/* Animation pour l'apparition du profil */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-header {
  animation: slideInDown 0.8s ease-out;
}

.stat-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* Animation pour la barre de progression */
.progress-bar {
  background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706);
  background-size: 200% 100%;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Effet de pulsation pour les statistiques importantes */
.pulse-stat {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Animation pour les badges */
.badge-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.badge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.badge-card:hover::before {
  left: 100%;
}

.badge-card:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* Styles pour les onglets */
.tab-button {
  transition: all 0.3s ease;
  position: relative;
}

.tab-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.tab-button.active::after {
  width: 100%;
}

/* Animation pour les scores */
.score-item {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.score-item:hover {
  transform: translateX(5px);
  border-left-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

/* Effet de brillance pour les éléments importants */
.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shine-sweep 3s infinite;
}

@keyframes shine-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Animation pour les icônes */
.icon-bounce {
  animation: iconBounce 2s infinite;
}

@keyframes iconBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Effet de rotation pour les icônes au survol */
.icon-rotate:hover {
  animation: rotate 0.5s ease-in-out;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Styles pour les boutons d'action */
.action-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.action-button:active {
  transform: translateY(-1px);
}

/* Animation pour les certificats */
.certificate-glow {
  animation: certificate-pulse 2s infinite;
}

@keyframes certificate-pulse {
  0% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.8);
  }
  100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
  }
}

/* Effet de confettis pour les réussites */
.celebration-effect {
  position: relative;
  overflow: hidden;
}

.celebration-effect::before {
  content: '🎉 🎊 ✨ 🌟 🎈';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 1.5rem;
  animation: confetti-fall 4s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-50px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(200px) rotate(720deg);
    opacity: 0;
  }
}

/* Styles pour les graphiques de statistiques */
.stat-bar {
  transition: width 1s ease-out;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.stat-bar:hover {
  background: linear-gradient(90deg, #2563eb, #7c3aed);
}

/* Animation de compteur */
.counter-animation {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .profile-grid {
    grid-template-columns: 1fr;
  }
  
  .tab-container {
    flex-direction: column;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
}

@media (max-width: 640px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .badge-grid {
    grid-template-columns: 1fr;
  }
  
  .score-item {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Effet de focus pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation de chargement */
.loading-skeleton {
  background: linear-gradient(90deg, #374151, #4b5563, #374151);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Styles pour les transitions fluides */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de glow pour les éléments spéciaux */
.glow-effect {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.8);
}
