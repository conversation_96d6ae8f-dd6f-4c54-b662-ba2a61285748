.star-rating-container {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star {
  transition: all 0.2s ease;
  user-select: none;
}

/* Tailles des étoiles */
.star-small {
  font-size: 16px;
}

.star-medium {
  font-size: 24px;
}

.star-large {
  font-size: 32px;
}

/* États des étoiles */
.star-filled {
  color: #ffd700;
  text-shadow: 0 0 3px rgba(255, 215, 0, 0.5);
}

.star-empty {
  color: #ddd;
}

.star-hovered {
  color: #ffed4e;
  text-shadow: 0 0 5px rgba(255, 237, 78, 0.7);
  transform: scale(1.1);
}

/* Interactivité */
.star-interactive {
  cursor: pointer;
}

.star-interactive:hover {
  transform: scale(1.1);
}

.star-readonly {
  cursor: default;
}

/* Texte de notation */
.rating-text {
  margin-left: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* Animation au survol */
@keyframes starPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.star-interactive:hover {
  animation: starPulse 0.3s ease;
}

/* Responsive */
@media (max-width: 768px) {
  .star-small {
    font-size: 14px;
  }
  
  .star-medium {
    font-size: 20px;
  }
  
  .star-large {
    font-size: 28px;
  }
  
  .rating-text {
    font-size: 12px;
  }
}
