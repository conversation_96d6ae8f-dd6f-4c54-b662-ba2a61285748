# Instructions pour configurer les endpoints Quiz

## Problème identifié
L'erreur 404 pour `/api/quiz/categories-with-challenges` indique que les routes quiz ne sont pas configurées dans votre serveur backend.

## Fichiers créés
1. `backend/routes/quiz.js` - Routes pour le système de quiz
2. `backend/models/UserProgress.js` - Modèle pour la progression utilisateur

## Étapes à suivre

### 1. Ajouter les routes quiz au serveur principal

Dans votre fichier principal du serveur (app.js, server.js, ou index.js), ajoutez cette ligne :

```javascript
// Ajouter cette ligne avec les autres routes
app.use('/api/quiz', require('./routes/quiz'));
```

### 2. Vérifier les modèles requis

Assurez-vous que ces modèles existent dans `backend/models/` :
- `Category.js`
- `Challenge.js` 
- `QuizScore.js` (<PERSON><PERSON><PERSON><PERSON>)
- `UserProgress.js` (créé)
- `Badge.js`

### 3. Structure attendue des modèles

#### Category.js
```javascript
const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Category', CategorySchema);
```

#### Challenge.js
```javascript
const mongoose = require('mongoose');

const ChallengeSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String },
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
  difficulty: { type: String, enum: ['easy', 'medium', 'hard'], default: 'medium' },
  questions: [{
    question: String,
    correct_answer: String,
    incorrect_answers: [String],
    difficulty: String,
    category: String
  }],
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Challenge', ChallengeSchema);
```

### 4. Endpoints créés

Les endpoints suivants sont maintenant disponibles :

- `GET /api/quiz/categories-with-challenges` - Récupère les catégories avec leurs challenges
- `GET /api/quiz/challenges/category/:categoryId` - Récupère les challenges d'une catégorie
- `POST /api/quiz/submit` - Soumet un quiz et calcule le score
- `GET /api/quiz/progress/:userId` - Récupère la progression d'un utilisateur
- `GET /api/quiz/leaderboard` - Récupère le classement général
- `GET /api/quiz/leaderboard/category/:categoryId` - Récupère le classement par catégorie

### 5. Correction du problème leaderboard

Les endpoints leaderboard retournent maintenant toujours un tableau, même en cas d'erreur, ce qui corrige l'erreur `findIndex is not a function`.

### 6. Test des endpoints

Après avoir ajouté les routes, redémarrez votre serveur backend et testez :

```bash
# Test de récupération des catégories
curl http://127.0.0.1:3000/api/quiz/categories-with-challenges

# Test du leaderboard
curl http://127.0.0.1:3000/api/quiz/leaderboard
```

### 7. Données de test

Pour tester, vous pouvez créer quelques catégories et challenges dans votre base de données MongoDB.

## Note importante

Assurez-vous que votre serveur backend est configuré avec :
- Express.js
- Mongoose pour MongoDB
- CORS pour permettre les requêtes depuis le frontend
- Body parser pour les requêtes POST

Une fois ces étapes complétées, l'erreur 404 devrait être résolue et vous pourrez récupérer les catégories pour passer les quiz.
