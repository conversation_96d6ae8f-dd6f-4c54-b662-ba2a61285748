import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';
import { BadgeService } from '../../../badges/service/badge.service';

@Component({
  selector: 'app-quiz-leaderboard',
  templateUrl: './quiz-leaderboard.component.html',
  styleUrls: ['./quiz-leaderboard.component.css']
})
export class QuizLeaderboardComponent implements OnInit {
  globalLeaderboard: any[] = [];
  categoryLeaderboards: { [key: string]: any[] } = {};
  categories: any[] = [];
  selectedCategory: string = 'global';
  loading = true;
  errorMessage: string | null = null;
  currentUser: any;

  constructor(
    private router: Router,
    private quizService: QuizService,
    private badgeService: BadgeService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadLeaderboards();
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    }
  }

  loadLeaderboards(): void {
    this.loading = true;

    // Charger le classement global
    this.quizService.getLeaderboard(20).subscribe({
      next: (leaderboard) => {
        this.globalLeaderboard = Array.isArray(leaderboard) ? leaderboard : [];
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement du classement:', error);
        this.errorMessage = 'Impossible de charger le classement.';
        this.globalLeaderboard = []; // S'assurer que c'est un tableau vide en cas d'erreur
        this.loading = false;
      }
    });

    // Charger les catégories pour les classements par catégorie
    this.loadCategories();
  }

  loadCategories(): void {
    this.quizService.getCategoriesWithChallenges().subscribe({
      next: (response: any) => {
        console.log('Response from getCategoriesWithChallenges:', response);

        // Gérer le cas où la réponse est un objet avec une propriété contenant le tableau
        let categories = response;

        // Si la réponse est un objet avec une propriété 'data', 'categories', etc.
        if (response && typeof response === 'object' && !Array.isArray(response)) {
          if (response.data && Array.isArray(response.data)) {
            categories = response.data;
          } else if (response.categories && Array.isArray(response.categories)) {
            categories = response.categories;
          } else if (response.results && Array.isArray(response.results)) {
            categories = response.results;
          } else {
            // Si aucune propriété connue, essayer de convertir en tableau
            categories = [];
            console.warn('Format de réponse inattendu pour les catégories:', response);
          }
        }

        // S'assurer que categories est un tableau
        this.categories = Array.isArray(categories) ? categories : [];

        // Charger le classement pour chaque catégorie
        this.categories.forEach(category => {
          this.loadCategoryLeaderboard(category._id);
        });
      },
      error: (error) => {
        console.error('Erreur lors du chargement des catégories:', error);
        this.categories = []; // S'assurer que c'est un tableau vide en cas d'erreur
      }
    });
  }

  loadCategoryLeaderboard(categoryId: string): void {
    this.quizService.getCategoryLeaderboard(categoryId, 10).subscribe({
      next: (leaderboard) => {
        this.categoryLeaderboards[categoryId] = Array.isArray(leaderboard) ? leaderboard : [];
      },
      error: (error) => {
        console.error(`Erreur lors du chargement du classement pour la catégorie ${categoryId}:`, error);
        this.categoryLeaderboards[categoryId] = []; // S'assurer que c'est un tableau vide en cas d'erreur
      }
    });
  }

  get currentLeaderboard(): any[] {
    if (this.selectedCategory === 'global') {
      return Array.isArray(this.globalLeaderboard) ? this.globalLeaderboard : [];
    }
    const categoryLeaderboard = this.categoryLeaderboards[this.selectedCategory];
    return Array.isArray(categoryLeaderboard) ? categoryLeaderboard : [];
  }

  get currentUserRank(): number {
    if (!this.currentUser) return -1;

    const leaderboard = this.currentLeaderboard;
    // Vérification supplémentaire pour s'assurer que leaderboard est un tableau
    if (!Array.isArray(leaderboard)) return -1;

    const userIndex = leaderboard.findIndex(user => user.userId === this.currentUser.id);
    return userIndex >= 0 ? userIndex + 1 : -1;
  }

  selectCategory(categoryId: string): void {
    this.selectedCategory = categoryId;
  }

  getRankIcon(rank: number): string {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  }

  getRankColor(rank: number): string {
    switch (rank) {
      case 1: return 'text-yellow-400';
      case 2: return 'text-gray-400';
      case 3: return 'text-orange-400';
      default: return 'text-white';
    }
  }

  getBadgeIcon(badgeType: string): string {
    switch (badgeType) {
      case 'EXPERT': return '💎';
      case 'INTERMEDIATE': return '🥇';
      case 'BEGINNER': return '🥈';
      case 'PARTICIPANT': return '🥉';
      default: return '🏅';
    }
  }

  getBadgeColor(badgeType: string): string {
    switch (badgeType) {
      case 'EXPERT': return 'from-purple-500 to-pink-500';
      case 'INTERMEDIATE': return 'from-blue-500 to-cyan-500';
      case 'BEGINNER': return 'from-green-500 to-teal-500';
      case 'PARTICIPANT': return 'from-gray-500 to-gray-600';
      default: return 'from-gray-500 to-gray-600';
    }
  }

  getCategoryName(categoryId: string): string {
    const category = this.categories.find(c => c._id === categoryId);
    return category ? category.name : 'Catégorie inconnue';
  }

  goToProfile(): void {
    this.router.navigate(['/quiz/profileQuiz']);
  }

  goToQuizzes(): void {
    this.router.navigate(['/quiz/categories']);
  }

  refreshLeaderboard(): void {
    this.loadLeaderboards();
  }

  shareLeaderboard(): void {
    const shareData = {
      title: 'Classement Quiz SkillsHub',
      text: 'Découvrez le classement des meilleurs joueurs de quiz sur SkillsHub !',
      url: window.location.href
    };

    if (navigator.share) {
      navigator.share(shareData).catch(err => console.log('Erreur lors du partage:', err));
    } else {
      navigator.clipboard.writeText(`${shareData.text} ${shareData.url}`).then(() => {
        alert('Lien du classement copié dans le presse-papiers !');
      }).catch(() => {
        alert('Impossible de copier le lien.');
      });
    }
  }

  formatScore(score: number): string {
    if (score >= 1000) {
      return `${(score / 1000).toFixed(1)}k`;
    }
    return score.toString();
  }

  getProgressToNextLevel(score: number): number {
    const nextMilestone = Math.ceil(score / 1000) * 1000;
    const previousMilestone = Math.floor(score / 1000) * 1000;
    const progress = ((score - previousMilestone) / (nextMilestone - previousMilestone)) * 100;
    return Math.min(progress, 100);
  }

  isCurrentUser(userId: string): boolean {
    return this.currentUser && this.currentUser.id === userId;
  }
}
