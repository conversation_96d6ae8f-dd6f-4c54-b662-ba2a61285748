export interface QuizScore {
  _id?: string;
  userId: string;
  challengeId: string;
  categoryId: string;
  score: number;
  totalQuestions: number;
  percentage: number;
  timeSpent?: number; // en secondes
  completedAt: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface UserProgress {
  _id?: string;
  userId: string;
  totalScore: number;
  totalQuizzes: number;
  averagePercentage: number;
  categoriesCompleted: string[];
  badgesEarned: string[];
  certificateEarned: boolean;
  certificateEarnedAt?: Date;
  lastQuizDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface QuizQuestion {
  _id?: string;
  question: string;
  correct_answer: string;
  incorrect_answers: string[];
  shuffledAnswers?: string[];
  selectedAnswer?: string | null;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
}

export interface QuizChallenge {
  _id: string;
  title: string;
  description: string;
  categoryId: string;
  categoryName?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questions: QuizQuestion[];
  totalQuestions: number;
  timeLimit?: number; // en minutes
  createdBy: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CategoryWithChallenges {
  _id: string;
  name: string;
  description: string;
  challenges: QuizChallenge[];
  totalChallenges: number;
  completedChallenges?: number;
  averageScore?: number;
}

export interface QuizResult {
  score: number;
  totalQuestions: number;
  percentage: number;
  timeSpent: number;
  correctAnswers: QuizQuestion[];
  incorrectAnswers: QuizQuestion[];
  badgeEarned?: {
    name: string;
    type: string;
    imageUrl?: string;
  };
  certificateEarned?: boolean;
  newTotalScore?: number;
}

export interface QuizSession {
  challengeId: string;
  userId: string;
  startTime: Date;
  currentQuestionIndex: number;
  answers: { [questionId: string]: string };
  isCompleted: boolean;
}
