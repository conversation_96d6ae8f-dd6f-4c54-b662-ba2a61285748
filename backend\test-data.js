// Script pour créer des données de test pour le système de quiz
// Exécutez ce script après avoir configuré votre base de données MongoDB

const mongoose = require('mongoose');

// Connexion à MongoDB (ajustez l'URL selon votre configuration)
const MONGODB_URI = 'mongodb://localhost:27017/skillshub'; // Remplacez par votre URI MongoDB

// Schémas simplifiés pour les tests
const CategorySchema = new mongoose.Schema({
  name: String,
  description: String,
  createdAt: { type: Date, default: Date.now }
});

const ChallengeSchema = new mongoose.Schema({
  title: String,
  description: String,
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  difficulty: { type: String, enum: ['easy', 'medium', 'hard'], default: 'medium' },
  questions: [{
    question: String,
    correct_answer: String,
    incorrect_answers: [String],
    difficulty: String,
    category: String
  }],
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now }
});

const Category = mongoose.model('Category', CategorySchema);
const Challenge = mongoose.model('Challenge', ChallengeSchema);

async function createTestData() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connecté à MongoDB');

    // Supprimer les données existantes (optionnel)
    await Category.deleteMany({});
    await Challenge.deleteMany({});
    console.log('Données existantes supprimées');

    // Créer des catégories de test
    const categories = await Category.insertMany([
      {
        name: 'JavaScript',
        description: 'Questions sur le langage JavaScript et ses frameworks'
      },
      {
        name: 'Python',
        description: 'Questions sur Python et ses bibliothèques'
      },
      {
        name: 'Angular',
        description: 'Framework Angular et TypeScript'
      },
      {
        name: 'Node.js',
        description: 'Développement backend avec Node.js'
      }
    ]);

    console.log('Catégories créées:', categories.length);

    // Créer des challenges de test
    const challenges = [];

    // Challenge JavaScript
    challenges.push({
      title: 'JavaScript Basics',
      description: 'Questions de base sur JavaScript',
      categoryId: categories[0]._id,
      difficulty: 'easy',
      questions: [
        {
          question: 'Quel est le type de données pour stocker du texte en JavaScript?',
          correct_answer: 'string',
          incorrect_answers: ['number', 'boolean', 'object'],
          difficulty: 'easy',
          category: 'JavaScript'
        },
        {
          question: 'Comment déclarer une variable en JavaScript?',
          correct_answer: 'var, let ou const',
          incorrect_answers: ['variable', 'declare', 'def'],
          difficulty: 'easy',
          category: 'JavaScript'
        },
        {
          question: 'Que retourne typeof null en JavaScript?',
          correct_answer: 'object',
          incorrect_answers: ['null', 'undefined', 'string'],
          difficulty: 'medium',
          category: 'JavaScript'
        }
      ],
      isActive: true
    });

    // Challenge Python
    challenges.push({
      title: 'Python Fundamentals',
      description: 'Concepts fondamentaux de Python',
      categoryId: categories[1]._id,
      difficulty: 'medium',
      questions: [
        {
          question: 'Comment créer une liste en Python?',
          correct_answer: '[]',
          incorrect_answers: ['{}', '()', '<>'],
          difficulty: 'easy',
          category: 'Python'
        },
        {
          question: 'Quel mot-clé est utilisé pour définir une fonction en Python?',
          correct_answer: 'def',
          incorrect_answers: ['function', 'func', 'define'],
          difficulty: 'easy',
          category: 'Python'
        }
      ],
      isActive: true
    });

    // Challenge Angular
    challenges.push({
      title: 'Angular Components',
      description: 'Composants et directives Angular',
      categoryId: categories[2]._id,
      difficulty: 'hard',
      questions: [
        {
          question: 'Quel décorateur est utilisé pour créer un composant Angular?',
          correct_answer: '@Component',
          incorrect_answers: ['@Directive', '@Injectable', '@NgModule'],
          difficulty: 'medium',
          category: 'Angular'
        },
        {
          question: 'Comment passer des données du parent vers l\'enfant en Angular?',
          correct_answer: '@Input()',
          incorrect_answers: ['@Output()', '@ViewChild()', '@HostBinding()'],
          difficulty: 'medium',
          category: 'Angular'
        }
      ],
      isActive: true
    });

    await Challenge.insertMany(challenges);
    console.log('Challenges créés:', challenges.length);

    console.log('Données de test créées avec succès!');
    console.log('\nVous pouvez maintenant tester les endpoints:');
    console.log('GET http://127.0.0.1:3000/api/quiz/categories-with-challenges');
    console.log('GET http://127.0.0.1:3000/api/quiz/leaderboard');

  } catch (error) {
    console.error('Erreur lors de la création des données de test:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Déconnecté de MongoDB');
  }
}

// Exécuter le script
if (require.main === module) {
  createTestData();
}

module.exports = { createTestData };
