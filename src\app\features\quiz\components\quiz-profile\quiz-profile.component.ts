import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';
import { BadgeService } from '../../../badges/service/badge.service';
import { UserProgress } from '../../models/quiz.models';
import { Badge } from '../../../badges/models/badge.model';

@Component({
  selector: 'app-quiz-profile',
  templateUrl: './quiz-profile.component.html',
  styleUrls: ['./quiz-profile.component.css']
})
export class QuizProfileComponent implements OnInit {
  currentUser: any;
  userProgress: UserProgress | null = null;
  userBadges: Badge[] = [];
  userScores: any[] = [];
  totalScore = 0;
  certificateProgress = 0;
  loading = true;
  errorMessage: string | null = null;
  selectedTab = 'overview';

  constructor(
    private router: Router,
    private quizService: QuizService,
    private badgeService: BadgeService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    if (this.currentUser) {
      this.loadUserData();
    }
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    } else {
      this.errorMessage = 'Utilisateur non connecté.';
      this.router.navigate(['/login']);
    }
  }

  loadUserData(): void {
    this.loading = true;

    // Charger la progression de l'utilisateur
    this.quizService.getUserProgress(this.currentUser.id).subscribe({
      next: (progress) => {
        this.userProgress = progress;
        this.totalScore = progress.totalScore || 0;
        this.certificateProgress = Math.min((this.totalScore / 1000) * 100, 100);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la progression:', error);
      }
    });

    // Charger les badges de l'utilisateur
    this.badgeService.getBadgesByUser(this.currentUser.id).subscribe({
      next: (badges) => {
        this.userBadges = badges;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des badges:', error);
      }
    });

    // Charger les scores de l'utilisateur
    this.quizService.getUserScores(this.currentUser.id).subscribe({
      next: (scores) => {
        this.userScores = scores.sort((a, b) =>
          new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
        );
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des scores:', error);
        this.loading = false;
      }
    });
  }

  get averageScore(): number {
    if (this.userScores.length === 0) return 0;
    const total = this.userScores.reduce((sum, score) => sum + score.percentage, 0);
    return Math.round(total / this.userScores.length);
  }

  get bestScore(): number {
    if (this.userScores.length === 0) return 0;
    return Math.max(...this.userScores.map(score => score.percentage));
  }

  get totalQuizzes(): number {
    return this.userScores.length;
  }

  get recentScores(): any[] {
    return this.userScores.slice(0, 5);
  }

  get badgesByType(): { [key: string]: Badge[] } {
    const grouped: { [key: string]: Badge[] } = {};
    this.userBadges.forEach(badge => {
      if (!grouped[badge.type]) {
        grouped[badge.type] = [];
      }
      grouped[badge.type].push(badge);
    });
    return grouped;
  }

  get objectKeys(): any {
    return Object.keys;
  }

  get scoresExcellent(): number {
    return this.userScores.filter(s => s.percentage >= 90).length;
  }

  get scoresGood(): number {
    return this.userScores.filter(s => s.percentage >= 70 && s.percentage < 90).length;
  }

  get scoresAverage(): number {
    return this.userScores.filter(s => s.percentage >= 50 && s.percentage < 70).length;
  }

  get scoresPoor(): number {
    return this.userScores.filter(s => s.percentage < 50).length;
  }

  get certificateEarned(): boolean {
    return this.userProgress?.certificateEarned || false;
  }

  get pointsToNextCertificate(): number {
    return Math.max(0, 1000 - this.totalScore);
  }

  selectTab(tab: string): void {
    this.selectedTab = tab;
  }

  goToQuizzes(): void {
    this.router.navigate(['/quiz/categories']);
  }

  goToLeaderboard(): void {
    this.router.navigate(['/quiz/leaderboard']);
  }

  getBadgeIcon(type: string): string {
    switch (type) {
      case 'EXPERT': return '💎';
      case 'INTERMEDIATE': return '🥇';
      case 'BEGINNER': return '🥈';
      case 'PARTICIPANT': return '🥉';
      default: return '🏅';
    }
  }

  getBadgeColor(type: string): string {
    switch (type) {
      case 'EXPERT': return 'from-purple-500 to-pink-500';
      case 'INTERMEDIATE': return 'from-blue-500 to-cyan-500';
      case 'BEGINNER': return 'from-green-500 to-teal-500';
      case 'PARTICIPANT': return 'from-gray-500 to-gray-600';
      default: return 'from-gray-500 to-gray-600';
    }
  }

  getScoreColor(percentage: number): string {
    if (percentage >= 90) return 'text-green-500';
    if (percentage >= 70) return 'text-blue-500';
    if (percentage >= 50) return 'text-yellow-500';
    return 'text-red-500';
  }

  getScoreIcon(percentage: number): string {
    if (percentage >= 90) return '🏆';
    if (percentage >= 70) return '🥇';
    if (percentage >= 50) return '🥈';
    return '🥉';
  }

  formatDate(date: string | Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  downloadCertificate(): void {
    if (this.certificateEarned) {
      this.badgeService.generateCertificatePDF(this.currentUser.id, {
        userName: this.currentUser.fullName || this.currentUser.name,
        totalScore: this.totalScore,
        completionDate: this.userProgress?.certificateEarnedAt || new Date()
      }).subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `certificat-${this.currentUser.fullName || 'utilisateur'}.pdf`;
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          console.error('Erreur lors du téléchargement du certificat:', error);
          alert('Erreur lors du téléchargement du certificat');
        }
      });
    }
  }

  shareProfile(): void {
    const shareData = {
      title: `Profil Quiz de ${this.currentUser.fullName || this.currentUser.name}`,
      text: `J'ai obtenu ${this.totalScore} points et ${this.userBadges.length} badges sur SkillsHub !`,
      url: window.location.href
    };

    if (navigator.share) {
      navigator.share(shareData).catch(err => console.log('Erreur lors du partage:', err));
    } else {
      navigator.clipboard.writeText(`${shareData.text} ${shareData.url}`).then(() => {
        alert('Profil copié dans le presse-papiers !');
      }).catch(() => {
        alert('Impossible de copier le profil.');
      });
    }
  }

  refreshData(): void {
    this.loadUserData();
  }
}
