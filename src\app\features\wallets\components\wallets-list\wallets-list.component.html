<div *ngIf="isLoading$ | async" class="spinner-overlay">
  <div class="spinner"></div>
</div>

<div class="p-6 bg-white rounded-xl shadow-lg" *ngIf="!(isLoading$ | async)">
  <h2 class="text-2xl font-bold text-[#6A00F4] mb-6">All Wallets</h2>

  <div *ngIf="(wallets$ | async)?.length === 0" class="text-center py-8 text-gray-500">
    No wallets found. Create a new wallet to get started.
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let wallet of wallets$ | async" 
         class="border border-gray-200 rounded-xl hover:shadow-md transition-shadow duration-300 overflow-hidden cursor-pointer"
         (click)="viewWalletDetails(wallet._id)">
      <div class="bg-gradient-to-r from-[#7F00FF] to-[#6A00F4] p-4 text-white">
        <h3 class="font-medium">{{ wallet.user?.fullName || 'User' }}'s Wallet</h3>
      </div>
      <div class="p-5">
        <div class="flex justify-between items-center mb-3">
          <span class="text-gray-600">Balance:</span>
          <span class="text-xl font-bold text-[#00CFFF]">{{ wallet.imoney?.value || 0 }}</span>
        </div>
        <div class="flex justify-between items-center mb-3">
          <span class="text-gray-600">Currency:</span>
          <span>{{ wallet.imoney?.currencyType || 'N/A' }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600">Status:</span>
          <span class="px-3 py-1 rounded-full text-sm font-medium" 
                [class.bg-green-100]="wallet.isActive" 
                [class.text-green-800]="wallet.isActive"
                [class.bg-red-100]="!wallet.isActive"
                [class.text-red-800]="!wallet.isActive">
            {{ wallet.isActive ? 'Active' : 'Deactivated' }}
          </span>
        </div>
        <button class="w-full mt-4 bg-[#7F00FF] hover:bg-[#6A00F4] text-white py-2 px-4 rounded-lg transition"
                (click)="$event.stopPropagation(); viewWalletDetails(wallet._id)">
          View Details
        </button>
      </div>
    </div>
  </div>
</div>
