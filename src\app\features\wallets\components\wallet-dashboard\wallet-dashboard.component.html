<div class="container mx-auto p-4">
  <!-- Loading spinner -->
  <div *ngIf="isLoading$ | async" class="flex justify-center items-center h-64">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>

  <!-- Error message -->
  <div *ngIf="error$ | async as error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
    <p>{{ error }}</p>
  </div>

  <!-- Wallet Dashboard -->
  <div *ngIf="wallet$ | async as wallet" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Wallet Card -->
    <div class="lg:col-span-1">
      <div class="bg-gradient-to-r from-purple-700 to-indigo-900 rounded-xl shadow-lg overflow-hidden">
        <div class="p-6">
          <h2 class="text-2xl font-bold text-white mb-4">My Wallet</h2>
          
          <div class="space-y-6">
            <!-- Balance -->
            <div>
              <p class="text-gray-300 text-sm">Current Balance</p>
              <p class="text-3xl font-bold text-cyan-400">{{ wallet.imoney?.value || 0 }}</p>
              <p class="text-gray-300 text-sm">{{ wallet.imoney?.currencyType || 'iMoney' }}</p>
            </div>
            
            <!-- Status -->
            <div>
              <p class="text-gray-300 text-sm">Status</p>
              <div class="flex items-center">
                <span class="inline-block w-3 h-3 rounded-full mr-2"
                      [class.bg-green-400]="wallet.isActive" 
                      [class.bg-red-400]="!wallet.isActive"></span>
                <span class="text-white">{{ wallet.isActive ? 'Active' : 'Inactive' }}</span>
              </div>
            </div>
            
            <!-- Actions -->
            <div class="flex space-x-3">
              <button 
                (click)="topUpWallet()" 
                [disabled]="!wallet.isActive"
                [class.opacity-50]="!wallet.isActive" 
                [class.cursor-not-allowed]="!wallet.isActive"
                class="flex-1 bg-pink-600 hover:bg-pink-700 text-white py-2 px-4 rounded-lg transition">
                Top Up
              </button>
              
              <button 
                (click)="viewTransactions()" 
                class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition">
                History
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Activity Summary -->
    <div class="lg:col-span-2">
      <div class="bg-white rounded-xl shadow-lg p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Recent Activity</h2>
        
        <!-- Placeholder for recent transactions -->
        <div class="space-y-4">
          <div *ngFor="let i of [1, 2, 3]" class="border-b border-gray-200 pb-3">
            <div class="flex justify-between items-center">
              <div>
                <p class="font-medium text-gray-800">{{ i === 1 ? 'Top Up' : i === 2 ? 'Course Purchase' : 'Skill Credit' }}</p>
                <p class="text-sm text-gray-500">{{ i === 1 ? '2 days ago' : i === 2 ? 'Yesterday' : 'Today' }}</p>
              </div>
              <div class="text-lg font-semibold" 
                   [class.text-green-500]="i === 1 || i === 3" 
                   [class.text-red-500]="i === 2">
                {{ i === 1 ? '+20' : i === 2 ? '-15' : '+5' }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt-4 text-center">
          <button 
            (click)="viewTransactions()" 
            class="text-indigo-600 hover:text-indigo-800 font-medium">
            View All Transactions
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- No Wallet Found -->
  <div *ngIf="(wallet$ | async) === null && !(isLoading$ | async) && !(error$ | async)" class="text-center py-12">
    <div class="bg-white rounded-xl shadow-lg p-8 max-w-md mx-auto">
      <h2 class="text-2xl font-bold text-gray-800 mb-4">No Wallet Found</h2>
      <p class="text-gray-600 mb-6">You don't have a wallet yet. Create one to start using iMoney.</p>
      <button class="bg-purple-600 hover:bg-purple-700 text-white py-3 px-6 rounded-lg transition">
        Create Wallet
      </button>
    </div>
  </div>
</div>
