import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-star-rating',
  templateUrl: './star-rating.component.html',
  styleUrls: ['./star-rating.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => StarRatingComponent),
      multi: true
    }
  ]
})
export class StarRatingComponent implements ControlValueAccessor {
  @Input() rating: number = 0;
  @Input() maxStars: number = 5;
  @Input() readonly: boolean = false;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Output() ratingChange = new EventEmitter<number>();

  hoveredStar: number = 0;
  stars: number[] = [];

  private onChange = (value: number) => {};
  private onTouched = () => {};

  ngOnInit() {
    this.stars = Array.from({ length: this.maxStars }, (_, i) => i + 1);
  }

  // ControlValueAccessor implementation
  writeValue(value: number): void {
    this.rating = value || 0;
  }

  registerOnChange(fn: (value: number) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.readonly = isDisabled;
  }

  // Star interaction methods
  onStarClick(star: number): void {
    if (this.readonly) return;
    
    this.rating = star;
    this.ratingChange.emit(this.rating);
    this.onChange(this.rating);
    this.onTouched();
  }

  onStarHover(star: number): void {
    if (this.readonly) return;
    this.hoveredStar = star;
  }

  onMouseLeave(): void {
    if (this.readonly) return;
    this.hoveredStar = 0;
  }

  getStarClass(star: number): string {
    const baseClass = 'star';
    const sizeClass = `star-${this.size}`;
    
    let stateClass = '';
    if (this.hoveredStar > 0) {
      stateClass = star <= this.hoveredStar ? 'star-hovered' : 'star-empty';
    } else {
      stateClass = star <= this.rating ? 'star-filled' : 'star-empty';
    }
    
    const interactiveClass = this.readonly ? 'star-readonly' : 'star-interactive';
    
    return `${baseClass} ${sizeClass} ${stateClass} ${interactiveClass}`;
  }

  getStarIcon(star: number): string {
    if (this.hoveredStar > 0) {
      return star <= this.hoveredStar ? '★' : '☆';
    }
    return star <= this.rating ? '★' : '☆';
  }
}
