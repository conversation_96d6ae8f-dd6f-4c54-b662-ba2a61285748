# 🚀 Guide de Démarrage Rapide - Correction Quiz

## 🎯 Objectif
R<PERSON> l'erreur 404 pour `/api/quiz/categories-with-challenges` et corriger les problèmes de leaderboard.

## 📋 Option 1 : Utiliser le nouveau serveur complet

### Étape 1 : Installer les dépendances backend
```bash
cd backend
npm install
```

### Étape 2 : Configurer MongoDB
Assurez-vous que MongoDB est démarré sur votre machine :
```bash
# Windows
net start MongoDB

# macOS/Linux
sudo systemctl start mongod
```

### Étape 3 : Créer des données de test
```bash
cd backend
node test-data.js
```

### Étape 4 : Démarrer le serveur
```bash
cd backend
npm start
```

### Étape 5 : Tester les endpoints
```bash
# Test de base
curl http://localhost:3000/api/test

# Test des catégories
curl http://localhost:3000/api/quiz/categories-with-challenges

# Test du leaderboard
curl http://localhost:3000/api/quiz/leaderboard
```

## 📋 Option 2 : Ajouter les routes à votre serveur existant

### Si vous avez déjà un serveur backend :

1. **Copiez les fichiers suivants dans votre projet :**
   - `backend/routes/quiz.js`
   - `backend/models/UserProgress.js`
   - `backend/models/Category.js` (si vous n'en avez pas)
   - `backend/models/Challenge.js` (si vous n'en avez pas)
   - `backend/models/Badge.js` (si vous n'en avez pas)

2. **Ajoutez cette ligne dans votre serveur principal :**
   ```javascript
   app.use('/api/quiz', require('./routes/quiz'));
   ```

3. **Redémarrez votre serveur**

## 🔧 Vérification

### 1. Vérifiez que le serveur démarre sans erreur
Vous devriez voir :
```
✅ Connecté à MongoDB
🚀 Serveur démarré sur le port 3000
📍 URL: http://localhost:3000
🔗 Test quiz: http://localhost:3000/api/quiz/categories-with-challenges
🏆 Test leaderboard: http://localhost:3000/api/quiz/leaderboard
```

### 2. Testez les endpoints dans votre navigateur
- http://localhost:3000/api/test
- http://localhost:3000/api/quiz/categories-with-challenges
- http://localhost:3000/api/quiz/leaderboard

### 3. Vérifiez dans votre application Angular
- Allez sur la page quiz : http://localhost:4200/quiz/categories
- L'erreur 404 devrait disparaître
- Les catégories devraient se charger
- Le leaderboard devrait fonctionner sans erreur

## 🐛 Dépannage

### Erreur de connexion MongoDB
```bash
# Vérifiez que MongoDB est démarré
mongosh
# ou
mongo
```

### Erreur "Cannot find module"
```bash
cd backend
npm install
```

### Port 3000 déjà utilisé
Modifiez le port dans `backend/app.js` :
```javascript
const PORT = process.env.PORT || 3001; // Changez 3000 en 3001
```

Et dans `src/environments/environment.ts` :
```typescript
const BASE_URL = "http://127.0.0.1:3001"; // Changez 3000 en 3001
```

### Les données ne s'affichent pas
1. Exécutez le script de données de test :
   ```bash
   cd backend
   node test-data.js
   ```

2. Vérifiez dans MongoDB :
   ```bash
   mongosh
   use skillshub
   db.categories.find()
   db.challenges.find()
   ```

## ✅ Résultat attendu

Après avoir suivi ces étapes :
- ❌ Plus d'erreur 404 pour les catégories
- ❌ Plus d'erreur `findIndex is not a function`
- ✅ Les catégories se chargent correctement
- ✅ Le leaderboard fonctionne
- ✅ Vous pouvez passer les quiz

## 📞 Support

Si vous rencontrez encore des problèmes :

1. **Vérifiez les logs du serveur** pour voir les erreurs
2. **Vérifiez la console du navigateur** pour les erreurs frontend
3. **Testez les endpoints directement** avec curl ou Postman
4. **Vérifiez que MongoDB contient des données** avec mongosh

## 🎉 Prochaines étapes

Une fois que tout fonctionne :
1. Ajoutez plus de catégories et challenges
2. Implémentez le système de badges complet
3. Ajoutez l'authentification aux routes quiz
4. Optimisez les performances avec la pagination
