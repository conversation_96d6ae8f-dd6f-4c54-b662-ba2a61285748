import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FeedbackRoutingModule } from './feedback-routing.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AverageRatingComponent } from './components/average-rating/average-rating.component';
import { TopRatedUsersComponent } from './components/top-rated-users/top-rated-users.component';
import { StarRatingComponent } from './components/star-rating/star-rating.component';
import { FeedbackModalComponent } from './components/feedback-modal/feedback-modal.component';


@NgModule({
  declarations: [
    AverageRatingComponent,
    TopRatedUsersComponent,
    StarRatingComponent,
    FeedbackModalComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FeedbackRoutingModule,
    RouterModule

  ]
})
export class FeedbackModule { }
