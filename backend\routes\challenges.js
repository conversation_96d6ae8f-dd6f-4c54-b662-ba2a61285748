const express = require('express');
const router = express.Router();
const Challenge = require('../models/Challenge');
const Category = require('../models/Category');

// GET /api/challenges/show - Récupérer tous les challenges
router.get('/show', async (req, res) => {
  try {
    console.log('Fetching all challenges for display');
    
    const challenges = await Challenge.find({ isActive: true })
      .populate('categoryId', 'name description')
      .sort({ createdAt: -1 });
    
    console.log('Challenges found:', challenges.length);
    
    // Enrichir les challenges avec des informations supplémentaires
    const enrichedChallenges = challenges.map(challenge => ({
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    }));
    
    res.json({
      success: true,
      data: enrichedChallenges,
      count: enrichedChallenges.length
    });
  } catch (error) {
    console.error('Error fetching challenges:', error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// GET /api/challenges/:id - Récupérer un challenge spécifique
router.get('/:id', async (req, res) => {
  try {
    console.log('Fetching challenge:', req.params.id);
    
    const challenge = await Challenge.findById(req.params.id)
      .populate('categoryId', 'name description');
    
    if (!challenge) {
      return res.status(404).json({ 
        success: false,
        error: 'Challenge not found' 
      });
    }
    
    // Enrichir le challenge avec des informations supplémentaires
    const enrichedChallenge = {
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    };
    
    res.json({
      success: true,
      data: enrichedChallenge
    });
  } catch (error) {
    console.error('Error fetching challenge by ID:', error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// GET /api/challenges/category/:categoryId - Récupérer les challenges par catégorie
router.get('/category/:categoryId', async (req, res) => {
  try {
    console.log('Fetching challenges for category:', req.params.categoryId);
    
    const challenges = await Challenge.find({ 
      categoryId: req.params.categoryId,
      isActive: true 
    }).populate('categoryId', 'name description');
    
    console.log('Challenges found for category:', challenges.length);
    
    // Enrichir les challenges avec des informations supplémentaires
    const enrichedChallenges = challenges.map(challenge => ({
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    }));
    
    res.json({
      success: true,
      data: enrichedChallenges,
      count: enrichedChallenges.length
    });
  } catch (error) {
    console.error('Error fetching challenges by category:', error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// POST /api/challenges - Créer un nouveau challenge
router.post('/', async (req, res) => {
  try {
    const challenge = new Challenge(req.body);
    await challenge.save();
    
    await challenge.populate('categoryId', 'name description');
    
    const enrichedChallenge = {
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    };
    
    res.status(201).json({
      success: true,
      data: enrichedChallenge
    });
  } catch (error) {
    console.error('Error creating challenge:', error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// PUT /api/challenges/:id - Mettre à jour un challenge
router.put('/:id', async (req, res) => {
  try {
    const challenge = await Challenge.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('categoryId', 'name description');
    
    if (!challenge) {
      return res.status(404).json({ 
        success: false,
        error: 'Challenge not found' 
      });
    }
    
    const enrichedChallenge = {
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    };
    
    res.json({
      success: true,
      data: enrichedChallenge
    });
  } catch (error) {
    console.error('Error updating challenge:', error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// DELETE /api/challenges/:id - Supprimer un challenge
router.delete('/:id', async (req, res) => {
  try {
    const challenge = await Challenge.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    
    if (!challenge) {
      return res.status(404).json({ 
        success: false,
        error: 'Challenge not found' 
      });
    }
    
    // Supprimer automatiquement les badges associés
    const Badge = require('../models/Badge');
    await Badge.deleteBadgesForChallenge(req.params.id);
    
    res.json({
      success: true,
      message: 'Challenge deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting challenge:', error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

module.exports = router;
