/* Animation pour l'apparition des questions */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.question-enter {
  animation: slideInRight 0.5s ease-out;
}

.question-exit {
  animation: slideInLeft 0.5s ease-out;
}

/* Styles pour les options de réponse */
.answer-option {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.answer-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.answer-option:hover::before {
  left: 100%;
}

.answer-option.selected {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Animation pour la barre de progression */
.progress-bar {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 200% 100%;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Effet de pulsation pour le timer */
.timer-warning {
  animation: pulse-red 1s infinite;
}

@keyframes pulse-red {
  0% {
    background-color: rgb(220, 38, 38);
    transform: scale(1);
  }
  50% {
    background-color: rgb(239, 68, 68);
    transform: scale(1.05);
  }
  100% {
    background-color: rgb(220, 38, 38);
    transform: scale(1);
  }
}

/* Styles pour les indicateurs de questions */
.question-indicator {
  transition: all 0.3s ease;
  cursor: pointer;
}

.question-indicator:hover {
  transform: scale(1.1);
}

.question-indicator.current {
  animation: pulse-blue 2s infinite;
}

@keyframes pulse-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Animation pour les boutons */
.btn-animated {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-animated:hover::before {
  left: 100%;
}

.btn-animated:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-animated:active {
  transform: translateY(0);
}

/* Styles pour les résultats */
.result-card {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.score-display {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Effet de confettis pour les bons scores */
.confetti-effect {
  position: relative;
  overflow: hidden;
}

.confetti-effect::after {
  content: '🎉 🎊 ✨ 🌟 🎈';
  position: absolute;
  top: -50px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 2rem;
  animation: confetti-fall 3s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-50px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(200px) rotate(360deg);
    opacity: 0;
  }
}

/* Styles pour les badges */
.badge-earned {
  animation: badge-bounce 1s ease-out;
}

@keyframes badge-bounce {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Effet de brillance pour les éléments importants */
.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .question-container {
    padding: 1rem;
  }
  
  .answer-option {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .btn-responsive {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .score-display {
    font-size: 3rem;
  }
  
  .question-indicator {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.75rem;
  }
}

/* Effet de focus pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation de chargement */
.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #ffffff;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles pour les transitions fluides */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de survol pour les cartes */
.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Animation pour les statistiques */
.stat-counter {
  animation: countUp 1.5s ease-out;
}

/* Effet de glow pour les éléments spéciaux */
.glow-effect {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.8);
}
