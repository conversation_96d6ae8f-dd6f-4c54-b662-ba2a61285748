import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Badge {
  _id: string;
  userId: string;
  challengeId?: string;
  categoryId: string;
  type: 'PARTICIPANT' | 'BEGINNER' | 'INTERMEDIATE' | 'EXPERT' | 'MASTER';
  name: string;
  description: string;
  imageUrl?: string;
  certificateImageUrl?: string;
  percentage: number;
  quizScore: number;
  scoreRequired: number;
  percentageRequired: number;
  awardedAt: Date;
  isActive: boolean;

  // Données populées
  user?: {
    _id: string;
    fullName: string;
    email: string;
  };
  category?: {
    _id: string;
    name: string;
    description: string;
  };
  challenge?: {
    _id: string;
    title: string;
    description: string;
  };
}

export interface BadgeStats {
  totalBadges: number;
  participantBadges: number;
  beginnerBadges: number;
  intermediateBadges: number;
  expertBadges: number;
  masterBadges: number;
  averagePercentage: number;
  totalScore: number;
  certificatesEarned: number;
}

@Injectable({
  providedIn: 'root'
})
export class BadgeService {
  private apiUrl = 'http://localhost:3001/api/badges';

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const user = localStorage.getItem('user');
    let userRole = 'client';
    let userId = '';

    if (user) {
      try {
        const parsedUser = JSON.parse(user);
        userRole = parsedUser.role || 'client';
        userId = parsedUser._id || parsedUser.id || '';
      } catch (error) {
        console.error('Erreur lors du parsing de l\'utilisateur:', error);
      }
    }

    const headers: any = {
      'Content-Type': 'application/json',
      'user-role': userRole
    };

    // N'ajouter user-id que s'il existe
    if (userId) {
      headers['user-id'] = userId;
    }

    return new HttpHeaders(headers);
  }

  // Récupérer tous les badges (selon le rôle)
  getAllBadges(): Observable<Badge[]> {
    return this.http.get<Badge[]>(`${this.apiUrl}`, { headers: this.getHeaders() });
  }

  // Récupérer un badge par ID
  getBadgeById(id: string): Observable<Badge> {
    return this.http.get<Badge>(`${this.apiUrl}/${id}`, { headers: this.getHeaders() });
  }

  // Récupérer les badges d'un utilisateur
  getUserBadges(userId: string): Observable<Badge[]> {
    return this.http.get<Badge[]>(`${this.apiUrl}/user/${userId}`, { headers: this.getHeaders() });
  }

  // Récupérer les badges par catégorie
  getBadgesByCategory(categoryId: string): Observable<Badge[]> {
    return this.http.get<Badge[]>(`${this.apiUrl}/category/${categoryId}`, { headers: this.getHeaders() });
  }

  // Récupérer le classement des badges
  getLeaderboard(limit: number = 10): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/leaderboard?limit=${limit}`);
  }

  // Récupérer les statistiques des badges d'un utilisateur
  getUserBadgeStats(userId: string): Observable<BadgeStats> {
    return this.http.get<BadgeStats>(`${this.apiUrl}/stats/${userId}`, { headers: this.getHeaders() });
  }

  // Récupérer les badges de l'utilisateur connecté
  getMyBadges(): Observable<Badge[]> {
    const user = localStorage.getItem('user');
    if (!user) {
      throw new Error('Utilisateur non connecté');
    }
    const userId = JSON.parse(user)._id;
    return this.getUserBadges(userId);
  }

  // Récupérer les statistiques de l'utilisateur connecté
  getMyBadgeStats(): Observable<BadgeStats> {
    const user = localStorage.getItem('user');
    if (!user) {
      throw new Error('Utilisateur non connecté');
    }
    const userId = JSON.parse(user)._id;
    return this.getUserBadgeStats(userId);
  }

  // Vérifier si l'utilisateur a un certificat
  hasCertificate(): Observable<boolean> {
    return new Observable(observer => {
      this.getMyBadgeStats().subscribe({
        next: (stats) => {
          observer.next(stats.certificatesEarned > 0);
          observer.complete();
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  // Récupérer les badges avec certificats
  getBadgesWithCertificates(): Observable<Badge[]> {
    return new Observable(observer => {
      this.getMyBadges().subscribe({
        next: (badges) => {
          const badgesWithCertificates = badges.filter(badge => badge.certificateImageUrl);
          observer.next(badgesWithCertificates);
          observer.complete();
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  // Utilitaires pour les badges
  getBadgeTypeColor(type: string): string {
    const colors = {
      'PARTICIPANT': '#4CAF50',
      'BEGINNER': '#2196F3',
      'INTERMEDIATE': '#FF9800',
      'EXPERT': '#9C27B0',
      'MASTER': '#F44336'
    };
    return colors[type as keyof typeof colors] || '#666';
  }

  getBadgeTypeIcon(type: string): string {
    const icons = {
      'PARTICIPANT': '🏅',
      'BEGINNER': '🌟',
      'INTERMEDIATE': '🏆',
      'EXPERT': '👑',
      'MASTER': '💎'
    };
    return icons[type as keyof typeof icons] || '🏅';
  }

  getBadgeTypeName(type: string): string {
    const names = {
      'PARTICIPANT': 'Participant',
      'BEGINNER': 'Débutant',
      'INTERMEDIATE': 'Intermédiaire',
      'EXPERT': 'Expert',
      'MASTER': 'Maître'
    };
    return names[type as keyof typeof names] || type;
  }
}
