/* Animation pour l'apparition du classement */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.leaderboard-item {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.leaderboard-item:nth-child(1) { animation-delay: 0.1s; }
.leaderboard-item:nth-child(2) { animation-delay: 0.2s; }
.leaderboard-item:nth-child(3) { animation-delay: 0.3s; }
.leaderboard-item:nth-child(4) { animation-delay: 0.4s; }
.leaderboard-item:nth-child(5) { animation-delay: 0.5s; }

/* Animation pour le podium */
.podium-first {
  animation: podiumRise 1s ease-out;
}

.podium-second {
  animation: podiumRise 1s ease-out 0.2s both;
}

.podium-third {
  animation: podiumRise 1s ease-out 0.4s both;
}

@keyframes podiumRise {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Effet de brillance pour le premier */
.winner-glow {
  animation: winner-pulse 2s infinite;
}

@keyframes winner-pulse {
  0% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.8);
  }
  100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
  }
}

/* Animation pour les rangs */
.rank-icon {
  transition: all 0.3s ease;
}

.rank-icon:hover {
  transform: scale(1.2) rotate(10deg);
}

/* Effet de survol pour les éléments du classement */
.leaderboard-row {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.leaderboard-row::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.leaderboard-row:hover::before {
  left: 100%;
}

.leaderboard-row:hover {
  transform: translateX(5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Animation pour les avatars */
.avatar {
  transition: all 0.3s ease;
  position: relative;
}

.avatar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(-45deg) translateX(-100%);
  transition: transform 0.6s;
}

.avatar:hover::after {
  transform: rotate(-45deg) translateX(100%);
}

.avatar:hover {
  transform: scale(1.1);
}

/* Effet de pulsation pour l'utilisateur actuel */
.current-user {
  animation: current-user-pulse 2s infinite;
}

@keyframes current-user-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Animation pour les barres de progression */
.progress-bar {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  background-size: 200% 100%;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Styles pour les badges */
.badge-icon {
  transition: all 0.3s ease;
}

.badge-icon:hover {
  transform: rotate(360deg) scale(1.2);
}

/* Animation pour les boutons de catégorie */
.category-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.category-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.category-button:hover::before {
  left: 100%;
}

.category-button:hover {
  transform: translateY(-2px);
}

/* Effet de confettis pour le podium */
.podium-celebration {
  position: relative;
  overflow: hidden;
}

.podium-celebration::before {
  content: '🎉 🎊 ✨ 🌟 🎈';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 1.2rem;
  animation: confetti-fall 4s ease-in-out infinite;
  opacity: 0.8;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-30px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(150px) rotate(720deg);
    opacity: 0;
  }
}

/* Animation pour les scores */
.score-counter {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Effet de brillance pour les éléments spéciaux */
.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shine-sweep 3s infinite;
}

@keyframes shine-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .podium-grid {
    grid-template-columns: 1fr;
  }
  
  .podium-first {
    order: 1;
  }
  
  .podium-second {
    order: 2;
  }
  
  .podium-third {
    order: 3;
  }
  
  .leaderboard-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .category-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 640px) {
  .podium-card {
    padding: 1rem;
  }
  
  .leaderboard-item {
    padding: 0.75rem;
  }
  
  .avatar {
    width: 2.5rem;
    height: 2.5rem;
  }
}

/* Effet de focus pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation de chargement */
.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #ffffff;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles pour les transitions fluides */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de glow pour les éléments importants */
.glow-effect {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.8);
}
