import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { QuizResult, QuizChallenge } from '../../models/quiz.models';

@Component({
  selector: 'app-quiz-results',
  templateUrl: './quiz-results.component.html',
  styleUrls: ['./quiz-results.component.css']
})
export class QuizResultsComponent implements OnInit {
  result: QuizResult | null = null;
  challenge: QuizChallenge | null = null;
  showCorrectAnswers = false;
  showIncorrectAnswers = false;

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Récupérer les données depuis l'état de navigation
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state) {
      this.result = navigation.extras.state['result'];
      this.challenge = navigation.extras.state['challenge'];
    }

    // Si pas de données, rediriger vers les catégories
    if (!this.result || !this.challenge) {
      this.router.navigate(['/quiz/categories']);
    }
  }

  get scoreColor(): string {
    if (!this.result) return 'text-gray-500';
    
    if (this.result.percentage >= 90) return 'text-green-500';
    if (this.result.percentage >= 70) return 'text-blue-500';
    if (this.result.percentage >= 50) return 'text-yellow-500';
    return 'text-red-500';
  }

  get scoreIcon(): string {
    if (!this.result) return '❓';
    
    if (this.result.percentage >= 90) return '🏆';
    if (this.result.percentage >= 70) return '🥇';
    if (this.result.percentage >= 50) return '🥈';
    return '🥉';
  }

  get performanceMessage(): string {
    if (!this.result) return '';
    
    if (this.result.percentage >= 90) return 'Excellent ! Vous maîtrisez parfaitement ce sujet !';
    if (this.result.percentage >= 70) return 'Très bien ! Vous avez une bonne compréhension du sujet.';
    if (this.result.percentage >= 50) return 'Bien joué ! Il y a encore quelques points à améliorer.';
    return 'Continuez vos efforts ! La pratique vous aidera à progresser.';
  }

  get badgeTypeColor(): string {
    if (!this.result?.badgeEarned) return '';
    
    switch (this.result.badgeEarned.type) {
      case 'EXPERT': return 'from-purple-500 to-pink-500';
      case 'INTERMEDIATE': return 'from-blue-500 to-cyan-500';
      case 'BEGINNER': return 'from-green-500 to-teal-500';
      case 'PARTICIPANT': return 'from-gray-500 to-gray-600';
      default: return 'from-gray-500 to-gray-600';
    }
  }

  get timeSpentFormatted(): string {
    if (!this.result) return '0:00';
    
    const minutes = Math.floor(this.result.timeSpent / 60);
    const seconds = this.result.timeSpent % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  get averageTimePerQuestion(): string {
    if (!this.result) return '0s';
    
    const avgSeconds = Math.round(this.result.timeSpent / this.result.totalQuestions);
    return `${avgSeconds}s`;
  }

  toggleCorrectAnswers(): void {
    this.showCorrectAnswers = !this.showCorrectAnswers;
  }

  toggleIncorrectAnswers(): void {
    this.showIncorrectAnswers = !this.showIncorrectAnswers;
  }

  goToCategories(): void {
    this.router.navigate(['/quiz/categories']);
  }

  goToCategoryQuizzes(): void {
    if (this.challenge?.categoryId) {
      this.router.navigate(['/quiz/category', this.challenge.categoryId]);
    } else {
      this.goToCategories();
    }
  }

  retakeQuiz(): void {
    if (this.challenge?._id) {
      this.router.navigate(['/quiz/play', this.challenge._id]);
    }
  }

  shareResults(): void {
    if (navigator.share && this.result && this.challenge) {
      navigator.share({
        title: `Résultats du Quiz: ${this.challenge.title}`,
        text: `J'ai obtenu ${this.result.percentage}% au quiz "${this.challenge.title}" sur SkillsHub !`,
        url: window.location.href
      }).catch(err => console.log('Erreur lors du partage:', err));
    } else {
      // Fallback: copier dans le presse-papiers
      const text = `J'ai obtenu ${this.result?.percentage}% au quiz "${this.challenge?.title}" sur SkillsHub !`;
      navigator.clipboard.writeText(text).then(() => {
        alert('Résultats copiés dans le presse-papiers !');
      }).catch(() => {
        alert('Impossible de copier les résultats.');
      });
    }
  }

  downloadCertificate(): void {
    if (this.result?.certificateEarned) {
      // Ici, vous pourriez implémenter la génération et le téléchargement du certificat
      alert('Fonctionnalité de téléchargement du certificat à implémenter');
    }
  }

  getQuestionDifficulty(question: any): string {
    return question.difficulty || this.challenge?.difficulty || 'medium';
  }

  getDifficultyColor(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }

  getDifficultyIcon(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return '🟢';
      case 'medium': return '🟡';
      case 'hard': return '🔴';
      default: return '⚪';
    }
  }
}
