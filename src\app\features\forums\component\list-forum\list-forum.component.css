/* Styles généraux */
body {
  font-family: 'Arial', sans-serif;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
}

/* Conteneur principal */
.container {
  max-width: 1200px;
  margin: auto;
}

/* Table styles */
.custom-table th, .custom-table td {
  vertical-align: middle;
  padding: 12px 20px;
}

.custom-table th {
  text-align: center;
  font-weight: bold;
}

.custom-table td {
  font-size: 14px;
}

/* Section de titre et boutons */
.d-flex.gap-3 {
  justify-content: center;
}

h2 {
  font-size: 24px;
  color: #343a40;
  font-weight: bold;
}

/* Boutons */
.custom-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 5px;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.custom-btn i {
  font-size: 18px; /* <PERSON>lle des icônes */
}

/* Effet au survol */
.custom-btn:hover {
  transform: translateY(-3px);
  cursor: pointer;
}

/* Bouton Ajouter Forum */
.btn-success.custom-btn {
  color: white;
  background-color: #28a745;
  border: 1px solid #28a745;
}

.btn-success.custom-btn:hover {
  background-color: #218838;
  border-color: #218838;
}

/* Bouton Modifier */
.btn-outline-primary.custom-btn {
  color: #0d6efd;
  border: 1px solid #0d6efd;
}

.btn-outline-primary.custom-btn:hover {
  background-color: #0d6efd;
  color: white;
}

/* Bouton Supprimer */
.btn-outline-danger.custom-btn {
  color: #dc3545;
  border: 1px solid #dc3545;
}

.btn-outline-danger.custom-btn:hover {
  background-color: #dc3545;
  color: white;
}

/* Effet sur les icônes */
.custom-btn i {
  transition: transform 0.2s ease;
}

.custom-btn:hover i {
  transform: scale(1.1);
}
