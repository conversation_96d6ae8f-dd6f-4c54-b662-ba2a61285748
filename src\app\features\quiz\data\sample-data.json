{"categories": [{"_id": "cat1", "name": "JavaScript", "description": "Questions sur le langage JavaScript et ses frameworks", "totalChallenges": 5, "completedChallenges": 2, "averageScore": 75}, {"_id": "cat2", "name": "Python", "description": "Questions sur Python et ses bibliothèques", "totalChallenges": 4, "completedChallenges": 1, "averageScore": 68}, {"_id": "cat3", "name": "Angular", "description": "Framework Angular et TypeScript", "totalChallenges": 3, "completedChallenges": 0, "averageScore": 0}], "challenges": [{"_id": "challenge1", "title": "JavaScript Fundamentals", "description": "Test your knowledge of JavaScript basics", "categoryId": "cat1", "categoryName": "JavaScript", "difficulty": "easy", "totalQuestions": 5, "timeLimit": 10, "isActive": true, "questions": [{"_id": "q1", "question": "What is the correct way to declare a variable in JavaScript?", "correct_answer": "let myVar = 5;", "incorrect_answers": ["variable myVar = 5;", "var myVar := 5;", "declare myVar = 5;"], "difficulty": "easy", "category": "JavaScript"}, {"_id": "q2", "question": "Which method is used to add an element to the end of an array?", "correct_answer": "push()", "incorrect_answers": ["add()", "append()", "insert()"], "difficulty": "easy", "category": "JavaScript"}, {"_id": "q3", "question": "What does '===' operator do in JavaScript?", "correct_answer": "Strict equality comparison", "incorrect_answers": ["Assignment", "Loose equality comparison", "Not equal comparison"], "difficulty": "medium", "category": "JavaScript"}, {"_id": "q4", "question": "Which of the following is NOT a JavaScript data type?", "correct_answer": "float", "incorrect_answers": ["string", "boolean", "number"], "difficulty": "easy", "category": "JavaScript"}, {"_id": "q5", "question": "What is the output of 'typeof null' in JavaScript?", "correct_answer": "object", "incorrect_answers": ["null", "undefined", "string"], "difficulty": "hard", "category": "JavaScript"}]}, {"_id": "challenge2", "title": "Python Basics", "description": "Fundamental concepts of Python programming", "categoryId": "cat2", "categoryName": "Python", "difficulty": "medium", "totalQuestions": 4, "timeLimit": 8, "isActive": true, "questions": [{"_id": "q6", "question": "Which keyword is used to define a function in Python?", "correct_answer": "def", "incorrect_answers": ["function", "func", "define"], "difficulty": "easy", "category": "Python"}, {"_id": "q7", "question": "What is the correct way to create a list in Python?", "correct_answer": "my_list = [1, 2, 3]", "incorrect_answers": ["my_list = (1, 2, 3)", "my_list = {1, 2, 3}", "my_list = <1, 2, 3>"], "difficulty": "easy", "category": "Python"}, {"_id": "q8", "question": "Which method is used to add an item to a Python list?", "correct_answer": "append()", "incorrect_answers": ["add()", "push()", "insert_end()"], "difficulty": "easy", "category": "Python"}, {"_id": "q9", "question": "What is the output of 'print(type([]))'?", "correct_answer": "<class 'list'>", "incorrect_answers": ["<class 'array'>", "<class 'tuple'>", "<class 'dict'>"], "difficulty": "medium", "category": "Python"}]}], "sampleScores": [{"_id": "score1", "userId": "user1", "challengeId": "challenge1", "categoryId": "cat1", "score": 40, "totalQuestions": 5, "percentage": 80, "timeSpent": 480, "completedAt": "2024-01-15T10:30:00Z"}, {"_id": "score2", "userId": "user1", "challengeId": "challenge2", "categoryId": "cat2", "score": 45, "totalQuestions": 4, "percentage": 75, "timeSpent": 360, "completedAt": "2024-01-16T14:20:00Z"}], "sampleBadges": [{"_id": "badge1", "userId": "user1", "challengeId": "challenge1", "name": "JavaScript Expert", "type": "INTERMEDIATE", "percentage": 80, "totalPercentage": 80, "awardedAt": "2024-01-15T10:30:00Z", "imageUrl": "/assets/badges/intermediate.png"}], "sampleUserProgress": {"_id": "progress1", "userId": "user1", "totalScore": 85, "totalQuizzes": 2, "averagePercentage": 77.5, "categoriesCompleted": ["cat1", "cat2"], "badgesEarned": ["badge1"], "certificateEarned": false, "lastQuizDate": "2024-01-16T14:20:00Z"}, "sampleLeaderboard": [{"userId": "user1", "userName": "<PERSON>", "totalScore": 850, "totalQuizzes": 12, "averageScore": 78, "highestBadge": "EXPERT"}, {"userId": "user2", "userName": "<PERSON>", "totalScore": 720, "totalQuizzes": 10, "averageScore": 72, "highestBadge": "INTERMEDIATE"}, {"userId": "user3", "userName": "<PERSON>", "totalScore": 650, "totalQuizzes": 8, "averageScore": 69, "highestBadge": "INTERMEDIATE"}]}