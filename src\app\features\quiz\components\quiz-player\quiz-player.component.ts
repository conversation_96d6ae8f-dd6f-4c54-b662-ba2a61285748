import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';
import { BadgeService } from '../../../badges/service/badge.service';
import { QuizChallenge, QuizQuestion, QuizResult, QuizSession } from '../../models/quiz.models';
import { Subscription, interval } from 'rxjs';

@Component({
  selector: 'app-quiz-player',
  templateUrl: './quiz-player.component.html',
  styleUrls: ['./quiz-player.component.css']
})
export class QuizPlayerComponent implements OnInit, OnDestroy {
  challengeId!: string;
  challenge: QuizChallenge | null = null;
  currentQuestionIndex = 0;
  selectedAnswer: string | null = null;
  answers: { [questionId: string]: string } = {};
  timeRemaining = 0;
  totalTime = 0;
  startTime!: Date;
  currentUser: any;
  loading = true;
  errorMessage: string | null = null;
  quizCompleted = false;
  quizResult: QuizResult | null = null;

  // Timer
  private timerSubscription?: Subscription;
  private sessionSubscription?: Subscription;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private quizService: QuizService,
    private badgeService: BadgeService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.challengeId = this.route.snapshot.paramMap.get('id') || '';
    this.loadChallenge();
  }

  ngOnDestroy(): void {
    this.stopTimer();
    if (this.sessionSubscription) {
      this.sessionSubscription.unsubscribe();
    }
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    } else {
      this.errorMessage = 'Utilisateur non connecté.';
      this.router.navigate(['/login']);
    }
  }

  loadChallenge(): void {
    this.loading = true;

    // Essayer de récupérer depuis localStorage d'abord
    const savedChallenge = localStorage.getItem('selectedChallenge');
    if (savedChallenge) {
      this.challenge = JSON.parse(savedChallenge);
      this.initializeQuiz();
    } else {
      // Sinon, récupérer depuis l'API
      this.quizService.getChallengeById(this.challengeId).subscribe({
        next: (challenge) => {
          this.challenge = challenge;
          this.initializeQuiz();
        },
        error: (error) => {
          console.error('Erreur lors du chargement du challenge:', error);
          this.errorMessage = 'Impossible de charger le challenge.';
          this.loading = false;
        }
      });
    }
  }

  initializeQuiz(): void {
    if (!this.challenge) return;

    // Mélanger les réponses pour chaque question
    this.challenge.questions = this.challenge.questions.map(q => ({
      ...q,
      shuffledAnswers: this.shuffleAnswers([...q.incorrect_answers, q.correct_answer])
    }));

    // Initialiser le timer si une limite de temps est définie
    if (this.challenge.timeLimit) {
      this.totalTime = this.challenge.timeLimit * 60; // Convertir en secondes
      this.timeRemaining = this.totalTime;
      this.startTimer();
    }

    // Démarrer la session de quiz
    this.startTime = new Date();
    this.quizService.startQuizSession(this.challengeId, this.currentUser.id).subscribe({
      next: (session) => {
        console.log('Session de quiz démarrée:', session);
      },
      error: (error) => {
        console.error('Erreur lors du démarrage de la session:', error);
      }
    });

    this.loading = false;
  }

  shuffleAnswers(answers: string[]): string[] {
    const shuffled = [...answers];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  startTimer(): void {
    this.timerSubscription = interval(1000).subscribe(() => {
      this.timeRemaining--;
      if (this.timeRemaining <= 0) {
        this.submitQuiz(); // Soumettre automatiquement quand le temps est écoulé
      }
    });
  }

  stopTimer(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
  }

  get currentQuestion(): QuizQuestion | null {
    if (!this.challenge || this.currentQuestionIndex >= this.challenge.questions.length) {
      return null;
    }
    return this.challenge.questions[this.currentQuestionIndex];
  }

  get progress(): number {
    if (!this.challenge) return 0;
    return Math.round(((this.currentQuestionIndex + 1) / this.challenge.questions.length) * 100);
  }

  get timeRemainingFormatted(): string {
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  selectAnswer(answer: string): void {
    this.selectedAnswer = answer;
  }

  nextQuestion(): void {
    if (!this.currentQuestion || !this.selectedAnswer) return;

    // Sauvegarder la réponse
    this.answers[this.currentQuestion._id || this.currentQuestionIndex.toString()] = this.selectedAnswer;
    this.quizService.saveAnswer(this.currentQuestion._id || this.currentQuestionIndex.toString(), this.selectedAnswer);

    // Passer à la question suivante
    if (this.currentQuestionIndex < this.challenge!.questions.length - 1) {
      this.currentQuestionIndex++;
      this.selectedAnswer = null;
      this.quizService.nextQuestion();
    } else {
      // Dernière question, soumettre le quiz
      this.submitQuiz();
    }
  }

  previousQuestion(): void {
    if (this.currentQuestionIndex > 0) {
      this.currentQuestionIndex--;
      // Récupérer la réponse précédemment sélectionnée
      const questionId = this.currentQuestion?._id || this.currentQuestionIndex.toString();
      this.selectedAnswer = this.answers[questionId] || null;
    }
  }

  submitQuiz(): void {
    if (!this.challenge || this.quizCompleted) return;

    this.stopTimer();
    this.loading = true;

    // Calculer le score
    let correctAnswers = 0;
    const endTime = new Date();
    const timeSpent = Math.round((endTime.getTime() - this.startTime.getTime()) / 1000);

    this.challenge.questions.forEach((question, index) => {
      const questionId = question._id || index.toString();
      const userAnswer = this.answers[questionId];
      if (userAnswer === question.correct_answer) {
        correctAnswers++;
      }
    });

    const percentage = this.quizService.calculatePercentage(correctAnswers, this.challenge.questions.length);
    const pointsPerQuestion = this.getPointsPerQuestion();
    const totalScore = correctAnswers * pointsPerQuestion;

    // Créer l'objet résultat
    this.quizResult = {
      score: totalScore,
      totalQuestions: this.challenge.questions.length,
      percentage: percentage,
      timeSpent: timeSpent,
      correctAnswers: this.challenge.questions.filter((q, i) =>
        this.answers[q._id || i.toString()] === q.correct_answer
      ),
      incorrectAnswers: this.challenge.questions.filter((q, i) =>
        this.answers[q._id || i.toString()] !== q.correct_answer
      )
    };

    // Sauvegarder le score
    this.saveQuizScore(totalScore, percentage, timeSpent);
  }

  private saveQuizScore(score: number, percentage: number, timeSpent: number): void {
    const scoreData = {
      userId: this.currentUser.id,
      challengeId: this.challengeId,
      categoryId: this.challenge?.categoryId || '',
      score: score,
      totalQuestions: this.challenge?.questions.length || 0,
      percentage: percentage,
      timeSpent: timeSpent,
      completedAt: new Date()
    };

    this.quizService.saveQuizScore(scoreData).subscribe({
      next: (savedScore) => {
        console.log('Score sauvegardé:', savedScore);

        // Attribuer un badge
        this.badgeService.assignBadge(this.currentUser.id, percentage).subscribe({
          next: (badge) => {
            if (badge) {
              this.quizResult!.badgeEarned = badge;
            }

            // Vérifier l'éligibilité au certificat
            this.checkCertificateEligibility();
          },
          error: (error) => {
            console.error('Erreur lors de l\'attribution du badge:', error);
            this.checkCertificateEligibility();
          }
        });
      },
      error: (error) => {
        console.error('Erreur lors de la sauvegarde du score:', error);
        this.loading = false;
        this.quizCompleted = true;
      }
    });
  }

  private checkCertificateEligibility(): void {
    this.quizService.checkCertificateEligibility(this.currentUser.id).subscribe({
      next: (result) => {
        if (result.eligible) {
          this.quizService.awardCertificate(this.currentUser.id).subscribe({
            next: () => {
              this.quizResult!.certificateEarned = true;
              this.quizResult!.newTotalScore = result.totalScore;
            },
            error: (error) => {
              console.error('Erreur lors de l\'attribution du certificat:', error);
            },
            complete: () => {
              this.loading = false;
              this.quizCompleted = true;
            }
          });
        } else {
          this.quizResult!.newTotalScore = result.totalScore;
          this.loading = false;
          this.quizCompleted = true;
        }
      },
      error: (error) => {
        console.error('Erreur lors de la vérification du certificat:', error);
        this.loading = false;
        this.quizCompleted = true;
      }
    });
  }

  private getPointsPerQuestion(): number {
    if (!this.challenge) return 10;

    switch (this.challenge.difficulty) {
      case 'easy': return 10;
      case 'medium': return 15;
      case 'hard': return 20;
      default: return 10;
    }
  }

  goToResults(): void {
    this.router.navigate(['/quiz/results'], {
      state: {
        result: this.quizResult,
        challenge: this.challenge
      }
    });
  }

  restartQuiz(): void {
    // Réinitialiser toutes les variables
    this.currentQuestionIndex = 0;
    this.selectedAnswer = null;
    this.answers = {};
    this.quizCompleted = false;
    this.quizResult = null;
    this.errorMessage = null;

    // Recharger le challenge
    this.loadChallenge();
  }

  exitQuiz(): void {
    this.router.navigate(['/quiz/categories']);
  }

  getTimeFormatted(timeInSeconds: number): string {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = timeInSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}
