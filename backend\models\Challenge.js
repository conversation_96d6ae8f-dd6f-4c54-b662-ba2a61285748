const mongoose = require('mongoose');

const QuestionSchema = new mongoose.Schema({
  question: {
    type: String,
    required: true
  },
  correct_answer: {
    type: String,
    required: true
  },
  incorrect_answers: [{
    type: String,
    required: true
  }],
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  category: {
    type: String
  },
  points: {
    type: Number,
    default: function() {
      switch(this.difficulty) {
        case 'easy': return 10;
        case 'medium': return 15;
        case 'hard': return 20;
        default: return 10;
      }
    }
  }
});

const ChallengeSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  difficulty: {
    type: String,
    enum: ['easy', 'medium', 'hard'],
    default: 'medium'
  },
  questions: [QuestionSchema],
  timeLimit: {
    type: Number, // en minutes
    default: 30
  },
  passingScore: {
    type: Number, // pourcentage minimum pour réussir
    default: 60
  },
  maxAttempts: {
    type: Number,
    default: 3
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  tags: [String],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index pour optimiser les requêtes
ChallengeSchema.index({ categoryId: 1, isActive: 1 });
ChallengeSchema.index({ difficulty: 1 });
ChallengeSchema.index({ title: 'text', description: 'text' });

// Middleware pour mettre à jour updatedAt
ChallengeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Méthodes d'instance
ChallengeSchema.methods.getTotalQuestions = function() {
  return this.questions.length;
};

ChallengeSchema.methods.getMaxScore = function() {
  return this.questions.reduce((total, question) => {
    return total + (question.points || 10);
  }, 0);
};

ChallengeSchema.methods.shuffleQuestions = function() {
  const shuffled = [...this.questions];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

// Méthodes statiques
ChallengeSchema.statics.getActiveChallenges = function() {
  return this.find({ isActive: true }).populate('categoryId');
};

ChallengeSchema.statics.getChallengesByCategory = function(categoryId) {
  return this.find({ 
    categoryId: categoryId, 
    isActive: true 
  }).populate('categoryId');
};

ChallengeSchema.statics.getChallengesByDifficulty = function(difficulty) {
  return this.find({ 
    difficulty: difficulty, 
    isActive: true 
  }).populate('categoryId');
};

module.exports = mongoose.model('Challenge', ChallengeSchema);
