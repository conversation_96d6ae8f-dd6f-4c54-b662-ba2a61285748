<div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900 p-6">
  <div class="max-w-6xl mx-auto">

    <!-- Header du profil -->
    <div class="bg-white/10 backdrop-blur-md rounded-xl p-8 mb-8">
      <div class="flex flex-col md:flex-row items-center justify-between">
        <div class="text-center md:text-left mb-6 md:mb-0">
          <div class="text-6xl mb-4">👤</div>
          <h1 class="text-3xl font-bold text-white mb-2">
            {{ currentUser?.fullName || currentUser?.name || 'Utilisateur' }}
          </h1>
          <p class="text-gray-300">{{ currentUser?.email }}</p>
        </div>

        <div class="text-center">
          <div class="text-4xl font-bold text-yellow-400 mb-2">{{ totalScore }}</div>
          <p class="text-gray-300">Points totaux</p>

          <!-- Barre de progression vers le certificat -->
          <div class="mt-4">
            <div class="text-sm text-gray-300 mb-2">
              Progression vers le certificat (1000 points)
            </div>
            <div class="w-48 bg-gray-700 rounded-full h-3">
              <div
                class="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-300"
                [style.width.%]="certificateProgress">
              </div>
            </div>
            <div class="text-xs text-gray-400 mt-1">
              {{ certificateProgress.toFixed(1) }}% - {{ pointsToNextCertificate }} points restants
            </div>
          </div>
        </div>
      </div>

      <!-- Certificat gagné -->
      <div *ngIf="certificateEarned" class="mt-6 p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg text-center">
        <div class="text-3xl mb-2">🎓</div>
        <h3 class="text-xl font-bold text-white mb-2">Certificat Obtenu !</h3>
        <p class="text-white mb-4">Félicitations ! Vous avez atteint 1000 points.</p>
        <button
          (click)="downloadCertificate()"
          class="px-6 py-2 bg-white text-orange-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
          📜 Télécharger le Certificat
        </button>
      </div>
    </div>

    <!-- Navigation par onglets -->
    <div class="bg-white/10 backdrop-blur-md rounded-xl mb-8">
      <div class="flex flex-wrap">
        <button
          (click)="selectTab('overview')"
          [class]="selectedTab === 'overview' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'"
          class="px-6 py-4 rounded-tl-xl transition-colors">
          📊 Vue d'ensemble
        </button>
        <button
          (click)="selectTab('badges')"
          [class]="selectedTab === 'badges' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'"
          class="px-6 py-4 transition-colors">
          🏆 Badges ({{ userBadges.length }})
        </button>
        <button
          (click)="selectTab('scores')"
          [class]="selectedTab === 'scores' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'"
          class="px-6 py-4 transition-colors">
          📈 Historique des scores
        </button>
        <button
          (click)="selectTab('stats')"
          [class]="selectedTab === 'stats' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'"
          class="px-6 py-4 rounded-tr-xl transition-colors">
          📋 Statistiques
        </button>
      </div>
    </div>

    <!-- Contenu des onglets -->
    <div class="space-y-8">

      <!-- Vue d'ensemble -->
      <div *ngIf="selectedTab === 'overview'">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
            <div class="text-3xl mb-2">🎯</div>
            <div class="text-2xl font-bold text-white">{{ totalQuizzes }}</div>
            <div class="text-gray-300">Quiz complétés</div>
          </div>

          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
            <div class="text-3xl mb-2">📊</div>
            <div class="text-2xl font-bold text-blue-400">{{ averageScore }}%</div>
            <div class="text-gray-300">Score moyen</div>
          </div>

          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
            <div class="text-3xl mb-2">🏆</div>
            <div class="text-2xl font-bold text-green-400">{{ bestScore }}%</div>
            <div class="text-gray-300">Meilleur score</div>
          </div>

          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 text-center">
            <div class="text-3xl mb-2">🏅</div>
            <div class="text-2xl font-bold text-yellow-400">{{ userBadges.length }}</div>
            <div class="text-gray-300">Badges gagnés</div>
          </div>
        </div>

        <!-- Scores récents -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
          <h3 class="text-xl font-bold text-white mb-6">📈 Scores récents</h3>
          <div *ngIf="recentScores.length > 0" class="space-y-4">
            <div *ngFor="let score of recentScores"
                 class="flex items-center justify-between p-4 bg-white/10 rounded-lg">
              <div class="flex items-center">
                <div class="text-2xl mr-4">{{ getScoreIcon(score.percentage) }}</div>
                <div>
                  <div class="text-white font-semibold">Quiz {{ score.challengeId }}</div>
                  <div class="text-gray-300 text-sm">{{ formatDate(score.completedAt) }}</div>
                </div>
              </div>
              <div class="text-right">
                <div [class]="getScoreColor(score.percentage)" class="text-xl font-bold">
                  {{ score.percentage }}%
                </div>
                <div class="text-gray-300 text-sm">{{ score.score }} points</div>
              </div>
            </div>
          </div>
          <div *ngIf="recentScores.length === 0" class="text-center text-gray-300 py-8">
            Aucun score enregistré pour le moment.
          </div>
        </div>
      </div>

      <!-- Badges -->
      <div *ngIf="selectedTab === 'badges'">
        <div *ngIf="userBadges.length > 0">
          <div *ngFor="let type of objectKeys(badgesByType)" class="mb-8">
            <h3 class="text-xl font-bold text-white mb-4">
              {{ getBadgeIcon(type) }} {{ type }} ({{ badgesByType[type].length }})
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div *ngFor="let badge of badgesByType[type]"
                   class="bg-gradient-to-r {{ getBadgeColor(badge.type) }} rounded-xl p-6">
                <div class="text-center">
                  <div class="text-4xl mb-3">{{ getBadgeIcon(badge.type) }}</div>
                  <h4 class="text-lg font-bold text-white mb-2">{{ badge.name }}</h4>
                  <p class="text-white/80 text-sm mb-2">{{ badge.percentage }}% de réussite</p>
                  <p class="text-white/60 text-xs">{{ formatDate(badge.awardedAt) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div *ngIf="userBadges.length === 0" class="text-center text-gray-300 py-12">
          <div class="text-6xl mb-4">🏆</div>
          <h3 class="text-xl font-bold mb-4">Aucun badge pour le moment</h3>
          <p class="mb-6">Complétez des quiz pour gagner vos premiers badges !</p>
          <button
            (click)="goToQuizzes()"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            Commencer un quiz
          </button>
        </div>
      </div>

      <!-- Historique des scores -->
      <div *ngIf="selectedTab === 'scores'">
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
          <h3 class="text-xl font-bold text-white mb-6">📈 Tous vos scores</h3>
          <div *ngIf="userScores.length > 0" class="space-y-4 max-h-96 overflow-y-auto">
            <div *ngFor="let score of userScores"
                 class="flex items-center justify-between p-4 bg-white/10 rounded-lg">
              <div class="flex items-center">
                <div class="text-2xl mr-4">{{ getScoreIcon(score.percentage) }}</div>
                <div>
                  <div class="text-white font-semibold">Quiz {{ score.challengeId }}</div>
                  <div class="text-gray-300 text-sm">
                    {{ score.totalQuestions }} questions - {{ formatDate(score.completedAt) }}
                  </div>
                </div>
              </div>
              <div class="text-right">
                <div [class]="getScoreColor(score.percentage)" class="text-xl font-bold">
                  {{ score.percentage }}%
                </div>
                <div class="text-gray-300 text-sm">{{ score.score }} points</div>
              </div>
            </div>
          </div>
          <div *ngIf="userScores.length === 0" class="text-center text-gray-300 py-8">
            Aucun score enregistré pour le moment.
          </div>
        </div>
      </div>

      <!-- Statistiques -->
      <div *ngIf="selectedTab === 'stats'">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
            <h3 class="text-lg font-bold text-white mb-4">📊 Répartition des scores</h3>
            <!-- Ici vous pourriez ajouter un graphique -->
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-300">90-100%:</span>
                <span class="text-green-400">{{ scoresExcellent }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">70-89%:</span>
                <span class="text-blue-400">{{ scoresGood }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">50-69%:</span>
                <span class="text-yellow-400">{{ scoresAverage }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-300">0-49%:</span>
                <span class="text-red-400">{{ scoresPoor }}</span>
              </div>
            </div>
          </div>

          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
            <h3 class="text-lg font-bold text-white mb-4">🎯 Objectifs</h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-gray-300">Certificat (1000 pts)</span>
                <span class="text-yellow-400">{{ certificateEarned ? '✅' : pointsToNextCertificate + ' pts restants' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="mt-8 flex flex-wrap gap-4 justify-center">
      <button
        (click)="goToQuizzes()"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        🎯 Faire un quiz
      </button>
      <button
        (click)="goToLeaderboard()"
        class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
        🏆 Classement
      </button>
      <button
        (click)="shareProfile()"
        class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
        📤 Partager
      </button>
      <button
        (click)="refreshData()"
        class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
        🔄 Actualiser
      </button>
    </div>
  </div>
</div>
