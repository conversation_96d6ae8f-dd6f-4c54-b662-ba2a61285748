<div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header avec navigation -->
    <div class="flex items-center justify-between mb-8">
      <button
        (click)="goBack()"
        class="flex items-center px-4 py-2 bg-white/10 backdrop-blur-md text-white rounded-lg hover:bg-white/20 transition-colors">
        ← Retour aux catégories
      </button>

      <div class="text-center">
        <h1 class="text-3xl font-bold text-white mb-2">
          {{ category?.name || 'Challenges' }}
        </h1>
        <p class="text-gray-300">{{ category?.description || 'Sélectionnez un challenge pour commencer' }}</p>
      </div>

      <button
        (click)="refreshChallenges()"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        🔄 Actualiser
      </button>
    </div>

    <!-- Filtres et recherche -->
    <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 mb-8">
      <div class="flex flex-col md:flex-row gap-4 items-center">
        <!-- Recherche -->
        <div class="flex-1">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="searchTerm = $any($event.target).value"
            placeholder="Rechercher un challenge..."
            class="w-full px-4 py-2 bg-white/20 text-white placeholder-gray-300 rounded-lg border border-white/30 focus:border-white/50 focus:outline-none">
        </div>

        <!-- Filtre par difficulté -->
        <div class="flex items-center space-x-2">
          <label class="text-white font-medium">Difficulté:</label>
          <select
            [(ngModel)]="selectedDifficulty"
            (change)="selectedDifficulty = $any($event.target).value"
            class="px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:border-white/50 focus:outline-none">
            <option value="all">Toutes</option>
            <option value="easy">Facile</option>
            <option value="medium">Moyen</option>
            <option value="hard">Difficile</option>
          </select>
        </div>

        <!-- Bouton clear filters -->
        <button
          (click)="clearFilters()"
          class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
          Effacer filtres
        </button>
      </div>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-500 text-white p-4 rounded-lg mb-6 text-center">
      {{ errorMessage }}
    </div>

    <!-- Loading -->
    <div *ngIf="loading" class="text-center">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      <p class="text-white mt-4">Chargement des challenges...</p>
    </div>

    <!-- Liste des challenges -->
    <div *ngIf="!loading && filteredChallenges.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        *ngFor="let challenge of filteredChallenges"
        class="bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition-all duration-300 transform hover:scale-105">

        <!-- Header du challenge -->
        <div class="flex items-start justify-between mb-4">
          <div class="text-4xl">{{ getChallengeIcon(challenge.title) }}</div>
          <span
            [class]="getDifficultyBadgeClass(challenge.difficulty)"
            class="px-2 py-1 rounded-full text-xs font-semibold">
            {{ getDifficultyIcon(challenge.difficulty) }} {{ challenge.difficulty | titlecase }}
          </span>
        </div>

        <!-- Titre et description -->
        <h3 class="text-xl font-bold text-white mb-3">{{ challenge.title }}</h3>
        <p class="text-gray-300 text-sm mb-4 line-clamp-3">{{ challenge.description }}</p>

        <!-- Statistiques du challenge -->
        <div class="space-y-2 mb-6">
          <div class="flex justify-between items-center">
            <span class="text-gray-300 text-sm">Questions:</span>
            <span class="text-white font-semibold">{{ challenge.totalQuestions }}</span>
          </div>

          <div class="flex justify-between items-center">
            <span class="text-gray-300 text-sm">Temps estimé:</span>
            <span class="text-white font-semibold">{{ getEstimatedTime(challenge.totalQuestions) }}</span>
          </div>

          <div class="flex justify-between items-center" *ngIf="challenge.timeLimit">
            <span class="text-gray-300 text-sm">Limite de temps:</span>
            <span class="text-yellow-400 font-semibold">{{ challenge.timeLimit }} min</span>
          </div>
        </div>

        <!-- Statut du challenge -->
        <div class="mb-4">
          <div *ngIf="challenge.isActive" class="flex items-center text-green-400 text-sm">
            <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
            Challenge actif
          </div>
          <div *ngIf="!challenge.isActive" class="flex items-center text-red-400 text-sm">
            <span class="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
            Challenge inactif
          </div>
        </div>

        <!-- Bouton de démarrage -->
        <button
          (click)="startQuiz(challenge)"
          [disabled]="!challenge.isActive || challenge.totalQuestions === 0"
          class="w-full py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
          <span *ngIf="challenge.isActive && challenge.totalQuestions > 0">🚀 Commencer le Quiz</span>
          <span *ngIf="!challenge.isActive">❌ Challenge inactif</span>
          <span *ngIf="challenge.isActive && challenge.totalQuestions === 0">❌ Aucune question</span>
        </button>
      </div>
    </div>

    <!-- Message si aucun challenge -->
    <div *ngIf="!loading && filteredChallenges.length === 0" class="text-center text-white">
      <div class="text-6xl mb-4">🎯</div>
      <h3 class="text-2xl font-bold mb-4">Aucun challenge trouvé</h3>
      <p class="text-gray-300 mb-6">
        <span *ngIf="challenges.length === 0">Aucun challenge disponible dans cette catégorie.</span>
        <span *ngIf="challenges.length > 0">Aucun challenge ne correspond à vos critères de recherche.</span>
      </p>
      <div class="space-x-4">
        <button
          (click)="clearFilters()"
          *ngIf="challenges.length > 0"
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Effacer les filtres
        </button>
        <button
          (click)="goBack()"
          class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
          Choisir une autre catégorie
        </button>
      </div>
    </div>

    <!-- Informations sur les récompenses -->
    <div class="mt-12 bg-white/10 backdrop-blur-md rounded-xl p-6">
      <h3 class="text-xl font-bold text-white mb-4 text-center">🎖️ Système de Points</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-2xl mb-2">🟢</div>
          <h4 class="text-white font-semibold">Facile</h4>
          <p class="text-gray-300 text-sm">10 points par bonne réponse</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-2xl mb-2">🟡</div>
          <h4 class="text-white font-semibold">Moyen</h4>
          <p class="text-gray-300 text-sm">15 points par bonne réponse</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-2xl mb-2">🔴</div>
          <h4 class="text-white font-semibold">Difficile</h4>
          <p class="text-gray-300 text-sm">20 points par bonne réponse</p>
        </div>
      </div>
    </div>
  </div>
</div>
