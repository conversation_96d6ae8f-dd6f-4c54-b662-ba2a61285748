<div class="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-4">
  <div class="max-w-4xl mx-auto">

    <!-- Loading -->
    <div *ngIf="loading && !quizCompleted" class="text-center">
      <div class="inline-block animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
      <p class="text-white mt-4 text-xl">Chargement du quiz...</p>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-500 text-white p-4 rounded-lg mb-6 text-center">
      {{ errorMessage }}
      <button (click)="exitQuiz()" class="ml-4 px-4 py-2 bg-red-700 rounded hover:bg-red-800">
        Retour
      </button>
    </div>

    <!-- Interface de quiz -->
    <div *ngIf="!loading && challenge && !quizCompleted" class="space-y-6">

      <!-- Header avec informations du quiz -->
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
        <div class="flex justify-between items-center mb-4">
          <h1 class="text-2xl font-bold text-white">{{ challenge.title }}</h1>
          <button
            (click)="exitQuiz()"
            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            ❌ Quitter
          </button>
        </div>

        <!-- Barre de progression -->
        <div class="mb-4">
          <div class="flex justify-between text-white text-sm mb-2">
            <span>Question {{ currentQuestionIndex + 1 }} sur {{ challenge.questions.length }}</span>
            <span>{{ progress }}% complété</span>
          </div>
          <div class="w-full bg-gray-700 rounded-full h-3">
            <div
              class="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-300"
              [style.width.%]="progress">
            </div>
          </div>
        </div>

        <!-- Timer (si applicable) -->
        <div *ngIf="challenge.timeLimit" class="text-center">
          <div class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg">
            ⏰ Temps restant: {{ timeRemainingFormatted }}
          </div>
        </div>
      </div>

      <!-- Question actuelle -->
      <div *ngIf="currentQuestion" class="bg-white/10 backdrop-blur-md rounded-xl p-8">
        <div class="mb-6">
          <div class="flex items-center mb-4">
            <span class="px-3 py-1 bg-blue-600 text-white rounded-full text-sm font-semibold mr-4">
              Question {{ currentQuestionIndex + 1 }}
            </span>
            <span class="px-3 py-1 bg-gray-600 text-white rounded-full text-sm">
              {{ challenge.difficulty | titlecase }}
            </span>
          </div>
          <h2 class="text-xl font-semibold text-white mb-6" [innerHTML]="currentQuestion.question"></h2>
        </div>

        <!-- Options de réponse -->
        <div class="space-y-3 mb-8">
          <div
            *ngFor="let answer of currentQuestion.shuffledAnswers; let i = index"
            class="p-4 rounded-lg border-2 cursor-pointer transition-all duration-200"
            [class]="selectedAnswer === answer ?
              'border-blue-500 bg-blue-500/20 text-white' :
              'border-gray-600 bg-white/5 text-gray-300 hover:border-gray-400 hover:bg-white/10'"
            (click)="selectAnswer(answer)">
            <div class="flex items-center">
              <div class="w-6 h-6 rounded-full border-2 mr-4 flex items-center justify-center"
                   [class]="selectedAnswer === answer ? 'border-blue-500 bg-blue-500' : 'border-gray-400'">
                <div *ngIf="selectedAnswer === answer" class="w-2 h-2 bg-white rounded-full"></div>
              </div>
              <span [innerHTML]="answer"></span>
            </div>
          </div>
        </div>

        <!-- Boutons de navigation -->
        <div class="flex justify-between">
          <button
            (click)="previousQuestion()"
            [disabled]="currentQuestionIndex === 0"
            class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
            ← Précédent
          </button>

          <button
            (click)="nextQuestion()"
            [disabled]="!selectedAnswer"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="currentQuestionIndex < challenge.questions.length - 1">Suivant →</span>
            <span *ngIf="currentQuestionIndex === challenge.questions.length - 1">Terminer 🏁</span>
          </button>
        </div>
      </div>

      <!-- Indicateur de questions -->
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4">
        <div class="flex flex-wrap gap-2 justify-center">
          <div
            *ngFor="let question of challenge.questions; let i = index"
            class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors"
            [class]="i === currentQuestionIndex ?
              'bg-blue-500 text-white' :
              answers[question._id || i.toString()] ?
                'bg-green-500 text-white' :
                'bg-gray-600 text-gray-300'">
            {{ i + 1 }}
          </div>
        </div>
      </div>
    </div>

    <!-- Résultats du quiz -->
    <div *ngIf="quizCompleted && quizResult" class="bg-white/10 backdrop-blur-md rounded-xl p-8 text-center">
      <div class="mb-6">
        <div class="text-6xl mb-4">
          <span *ngIf="quizResult.percentage >= 80">🎉</span>
          <span *ngIf="quizResult.percentage >= 60 && quizResult.percentage < 80">👏</span>
          <span *ngIf="quizResult.percentage >= 40 && quizResult.percentage < 60">👍</span>
          <span *ngIf="quizResult.percentage < 40">💪</span>
        </div>
        <h2 class="text-3xl font-bold text-white mb-4">Quiz Terminé !</h2>

        <!-- Score principal -->
        <div class="text-6xl font-bold text-white mb-4">{{ quizResult.percentage }}%</div>
        <p class="text-xl text-gray-300 mb-6">
          {{ quizResult.correctAnswers.length }} bonnes réponses sur {{ quizResult.totalQuestions }}
        </p>
      </div>

      <!-- Statistiques détaillées -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-2xl font-bold text-blue-400">{{ quizResult.score }}</div>
          <div class="text-gray-300">Points gagnés</div>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-2xl font-bold text-green-400">{{ getTimeFormatted(quizResult.timeSpent) }}</div>
          <div class="text-gray-300">Temps passé</div>
        </div>
        <div class="bg-white/10 rounded-lg p-4" *ngIf="quizResult.newTotalScore">
          <div class="text-2xl font-bold text-yellow-400">{{ quizResult.newTotalScore }}</div>
          <div class="text-gray-300">Score total</div>
        </div>
      </div>

      <!-- Badge gagné -->
      <div *ngIf="quizResult.badgeEarned" class="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg p-4 mb-6">
        <h3 class="text-xl font-bold text-white mb-2">🏆 Nouveau Badge Gagné !</h3>
        <p class="text-white">{{ quizResult.badgeEarned.name }} - {{ quizResult.badgeEarned.type }}</p>
      </div>

      <!-- Certificat gagné -->
      <div *ngIf="quizResult.certificateEarned" class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 mb-6">
        <h3 class="text-2xl font-bold text-white mb-2">🎓 Félicitations !</h3>
        <p class="text-white text-lg">Vous avez atteint 1000 points et gagné votre certificat !</p>
      </div>

      <!-- Boutons d'action -->
      <div class="space-y-4">
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            (click)="restartQuiz()"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            🔄 Refaire ce quiz
          </button>
          <button
            (click)="exitQuiz()"
            class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            🏠 Retour aux catégories
          </button>
          <button
            (click)="goToResults()"
            class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            📊 Voir les détails
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
