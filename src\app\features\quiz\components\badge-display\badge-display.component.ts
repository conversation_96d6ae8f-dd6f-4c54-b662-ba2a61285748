import { Component, OnInit } from '@angular/core';
import { BadgeService, Badge, BadgeStats } from '../../services/badge.service';

@Component({
  selector: 'app-badge-display',
  templateUrl: './badge-display.component.html',
  styleUrls: ['./badge-display.component.css']
})
export class BadgeDisplayComponent implements OnInit {
  badges: Badge[] = [];
  badgeStats: BadgeStats | null = null;
  certificateBadges: Badge[] = [];
  loading = true;
  errorMessage: string | null = null;
  
  // Filtres
  selectedType: string = 'all';
  selectedCategory: string = 'all';
  
  // Catégories uniques
  categories: { id: string; name: string }[] = [];

  constructor(private badgeService: BadgeService) {}

  ngOnInit(): void {
    this.loadBadges();
    this.loadBadgeStats();
    this.loadCertificates();
  }

  loadBadges(): void {
    this.loading = true;
    this.badgeService.getMyBadges().subscribe({
      next: (badges) => {
        this.badges = badges;
        this.extractCategories();
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des badges:', error);
        this.errorMessage = 'Impossible de charger vos badges.';
        this.loading = false;
      }
    });
  }

  loadBadgeStats(): void {
    this.badgeService.getMyBadgeStats().subscribe({
      next: (stats) => {
        this.badgeStats = stats;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }

  loadCertificates(): void {
    this.badgeService.getBadgesWithCertificates().subscribe({
      next: (certificates) => {
        this.certificateBadges = certificates;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des certificats:', error);
      }
    });
  }

  extractCategories(): void {
    const categoryMap = new Map();
    this.badges.forEach(badge => {
      if (badge.category) {
        categoryMap.set(badge.category._id, {
          id: badge.category._id,
          name: badge.category.name
        });
      }
    });
    this.categories = Array.from(categoryMap.values());
  }

  getFilteredBadges(): Badge[] {
    return this.badges.filter(badge => {
      const matchesType = this.selectedType === 'all' || badge.type === this.selectedType;
      const matchesCategory = this.selectedCategory === 'all' || 
        (badge.category && badge.category._id === this.selectedCategory);
      
      return matchesType && matchesCategory && badge.isActive;
    });
  }

  getBadgeTypeColor(type: string): string {
    return this.badgeService.getBadgeTypeColor(type);
  }

  getBadgeTypeIcon(type: string): string {
    return this.badgeService.getBadgeTypeIcon(type);
  }

  getBadgeTypeName(type: string): string {
    return this.badgeService.getBadgeTypeName(type);
  }

  downloadCertificate(badge: Badge): void {
    if (!badge.certificateImageUrl) return;
    
    // Créer un lien de téléchargement
    const link = document.createElement('a');
    link.href = badge.certificateImageUrl;
    link.download = `Certificat_${badge.name}_${new Date().toISOString().split('T')[0]}.svg`;
    link.click();
  }

  shareBadge(badge: Badge): void {
    if (navigator.share) {
      navigator.share({
        title: `Badge ${badge.name}`,
        text: `J'ai obtenu le badge ${badge.name} avec ${badge.percentage}% de réussite !`,
        url: window.location.href
      });
    } else {
      // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
      const text = `J'ai obtenu le badge ${badge.name} avec ${badge.percentage}% de réussite ! ${window.location.href}`;
      navigator.clipboard.writeText(text).then(() => {
        alert('Lien copié dans le presse-papiers !');
      });
    }
  }

  getProgressPercentage(current: number, total: number): number {
    return total > 0 ? Math.round((current / total) * 100) : 0;
  }

  clearFilters(): void {
    this.selectedType = 'all';
    this.selectedCategory = 'all';
  }

  refreshBadges(): void {
    this.loadBadges();
    this.loadBadgeStats();
    this.loadCertificates();
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getScoreColor(percentage: number): string {
    if (percentage >= 90) return 'text-green-500';
    if (percentage >= 70) return 'text-blue-500';
    if (percentage >= 50) return 'text-yellow-500';
    return 'text-red-500';
  }

  getScoreBadgeClass(percentage: number): string {
    if (percentage >= 90) return 'bg-green-100 text-green-800';
    if (percentage >= 70) return 'bg-blue-100 text-blue-800';
    if (percentage >= 50) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  }
}
