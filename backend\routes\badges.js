const express = require('express');
const router = express.Router();
const Badge = require('../models/Badge');
const Challenge = require('../models/Challenge');

// Middleware pour vérifier le rôle utilisateur
const checkUserRole = (req, res, next) => {
  const userRole = req.headers['user-role'] || req.user?.role || 'client';
  const userId = req.headers['user-id'] || req.user?.id;
  
  req.userRole = userRole;
  req.userId = userId;
  next();
};

// GET /api/badges - Récupérer les badges (Admin: tous, Client: seulement les siens)
router.get('/', checkUserRole, async (req, res) => {
  try {
    let badges;
    
    if (req.userRole === 'admin') {
      // Admin peut voir tous les badges
      badges = await Badge.find({ isActive: true })
        .populate('userId', 'fullName email')
        .populate('categoryId', 'name description')
        .populate('challengeId', 'title description')
        .sort({ awardedAt: -1 });
    } else {
      // Client ne peut voir que ses badges
      badges = await Badge.getUserBadges(req.userId);
    }
    
    res.json(badges);
  } catch (error) {
    console.error('Erreur lors de la récupération des badges:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/badges/:id - Récupérer un badge spécifique
router.get('/:id', checkUserRole, async (req, res) => {
  try {
    const badge = await Badge.findById(req.params.id)
      .populate('userId', 'fullName email')
      .populate('categoryId', 'name description')
      .populate('challengeId', 'title description');
    
    if (!badge) {
      return res.status(404).json({ error: 'Badge non trouvé' });
    }
    
    // Vérifier les permissions
    if (req.userRole !== 'admin' && badge.userId.toString() !== req.userId) {
      return res.status(403).json({ error: 'Accès non autorisé' });
    }
    
    res.json(badge);
  } catch (error) {
    console.error('Erreur lors de la récupération du badge:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/badges/user/:userId - Récupérer les badges d'un utilisateur spécifique
router.get('/user/:userId', checkUserRole, async (req, res) => {
  try {
    const targetUserId = req.params.userId;
    
    // Vérifier les permissions
    if (req.userRole !== 'admin' && targetUserId !== req.userId) {
      return res.status(403).json({ error: 'Vous ne pouvez voir que vos propres badges' });
    }
    
    const badges = await Badge.getUserBadges(targetUserId);
    res.json(badges);
  } catch (error) {
    console.error('Erreur lors de la récupération des badges utilisateur:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/badges/category/:categoryId - Récupérer les badges par catégorie
router.get('/category/:categoryId', checkUserRole, async (req, res) => {
  try {
    const categoryId = req.params.categoryId;
    let badges;
    
    if (req.userRole === 'admin') {
      // Admin peut voir tous les badges de la catégorie
      badges = await Badge.find({ 
        categoryId: categoryId, 
        isActive: true 
      })
        .populate('userId', 'fullName email')
        .populate('categoryId', 'name description')
        .populate('challengeId', 'title description')
        .sort({ awardedAt: -1 });
    } else {
      // Client ne peut voir que ses badges de cette catégorie
      badges = await Badge.getBadgesByCategory(req.userId, categoryId);
    }
    
    res.json(badges);
  } catch (error) {
    console.error('Erreur lors de la récupération des badges par catégorie:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/badges/leaderboard - Récupérer le classement des badges
router.get('/leaderboard', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const leaderboard = await Badge.getLeaderboard(limit);
    res.json(leaderboard);
  } catch (error) {
    console.error('Erreur lors de la récupération du classement:', error);
    res.status(500).json({ error: error.message });
  }
});

// Les routes POST, PUT, DELETE sont désactivées car les badges sont gérés automatiquement

// POST /api/badges - DÉSACTIVÉ (badges créés automatiquement)
router.post('/', (req, res) => {
  res.status(403).json({ 
    error: 'Les badges sont créés automatiquement lors de la completion des quiz. Création manuelle non autorisée.' 
  });
});

// PUT /api/badges/:id - DÉSACTIVÉ (badges gérés automatiquement)
router.put('/:id', (req, res) => {
  res.status(403).json({ 
    error: 'Les badges sont gérés automatiquement. Modification manuelle non autorisée.' 
  });
});

// DELETE /api/badges/:id - DÉSACTIVÉ (badges supprimés automatiquement avec les challenges)
router.delete('/:id', (req, res) => {
  res.status(403).json({ 
    error: 'Les badges sont supprimés automatiquement avec les challenges. Suppression manuelle non autorisée.' 
  });
});

// GET /api/badges/stats/:userId - Statistiques des badges d'un utilisateur
router.get('/stats/:userId', checkUserRole, async (req, res) => {
  try {
    const targetUserId = req.params.userId;
    
    // Vérifier les permissions
    if (req.userRole !== 'admin' && targetUserId !== req.userId) {
      return res.status(403).json({ error: 'Vous ne pouvez voir que vos propres statistiques' });
    }
    
    const stats = await Badge.aggregate([
      { $match: { userId: new Badge.base.Types.ObjectId(targetUserId), isActive: true } },
      {
        $group: {
          _id: null,
          totalBadges: { $sum: 1 },
          participantBadges: { $sum: { $cond: [{ $eq: ['$type', 'PARTICIPANT'] }, 1, 0] } },
          beginnerBadges: { $sum: { $cond: [{ $eq: ['$type', 'BEGINNER'] }, 1, 0] } },
          intermediateBadges: { $sum: { $cond: [{ $eq: ['$type', 'INTERMEDIATE'] }, 1, 0] } },
          expertBadges: { $sum: { $cond: [{ $eq: ['$type', 'EXPERT'] }, 1, 0] } },
          masterBadges: { $sum: { $cond: [{ $eq: ['$type', 'MASTER'] }, 1, 0] } },
          averagePercentage: { $avg: '$percentage' },
          totalScore: { $sum: '$quizScore' },
          certificatesEarned: { $sum: { $cond: [{ $ne: ['$certificateImageUrl', null] }, 1, 0] } }
        }
      }
    ]);
    
    const result = stats.length > 0 ? stats[0] : {
      totalBadges: 0,
      participantBadges: 0,
      beginnerBadges: 0,
      intermediateBadges: 0,
      expertBadges: 0,
      masterBadges: 0,
      averagePercentage: 0,
      totalScore: 0,
      certificatesEarned: 0
    };
    
    res.json(result);
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques:', error);
    res.status(500).json({ error: error.message });
  }
});

// Webhook pour supprimer automatiquement les badges quand un challenge est supprimé
router.post('/cleanup/challenge/:challengeId', async (req, res) => {
  try {
    const challengeId = req.params.challengeId;
    
    // Vérifier que le challenge existe encore
    const challenge = await Challenge.findById(challengeId);
    if (challenge && challenge.isActive) {
      return res.status(400).json({ error: 'Le challenge est encore actif' });
    }
    
    const result = await Badge.deleteBadgesForChallenge(challengeId);
    res.json({
      message: 'Badges nettoyés avec succès',
      badgesAffected: result.modifiedCount
    });
  } catch (error) {
    console.error('Erreur lors du nettoyage des badges:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
