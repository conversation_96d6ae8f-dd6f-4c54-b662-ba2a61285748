import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Feedback } from '../models/feedback.models';

@Injectable({
  providedIn: 'root'
})
export class FeedbackService {

  private baseUrl = 'http://localhost:3000/api/feedbacks'; // Remplacez par l'URL de votre backend

  constructor(private http: HttpClient) {}

  private getHeaders(): HttpHeaders {
    const user = localStorage.getItem('user');
    let userRole = 'client';
    let userId = '';

    if (user) {
      try {
        const parsedUser = JSON.parse(user);
        userRole = parsedUser.role || 'client';
        userId = parsedUser._id || parsedUser.id || '';
      } catch (error) {
        console.error('Erreur lors du parsing de l\'utilisateur:', error);
      }
    }

    const headers: any = {
      'Content-Type': 'application/json',
      'user-role': userRole
    };

    // N'ajouter user-id que s'il existe
    if (userId) {
      headers['user-id'] = userId;
    }

    return new HttpHeaders(headers);
  }

  // Créer un feedback
  createFeedback(feedback: any): Observable<Feedback> {
    return this.http.post<Feedback>(`${this.baseUrl}`, feedback, { headers: this.getHeaders() });
  }

  // Récupérer tous les feedbacks (selon le rôle)
  getAllFeedbacks(): Observable<Feedback[]> {
    return this.http.get<Feedback[]>(`${this.baseUrl}`, { headers: this.getHeaders() });
  }

  // Récupérer un feedback par ID
  getFeedbackById(id: string): Observable<Feedback> {
    return this.http.get<Feedback>(`${this.baseUrl}/${id}`, { headers: this.getHeaders() });
  }

  // Mettre à jour un feedback
  updateFeedback(id: string, feedback: any): Observable<Feedback> {
    return this.http.put<Feedback>(`${this.baseUrl}/${id}`, feedback, { headers: this.getHeaders() });
  }

  // Supprimer un feedback
  deleteFeedback(id: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/${id}`, { headers: this.getHeaders() });
  }

  // Récupérer les feedbacks d'un salon
  getFeedbacksForSalon(salonId: string): Observable<Feedback[]> {
    return this.http.get<Feedback[]>(`${this.baseUrl}/salon/${salonId}`, { headers: this.getHeaders() });
  }

  // Récupérer la note moyenne d'un utilisateur
  getUserAverageRating(userId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/user/${userId}/average`);
  }

  // Déclencher les feedbacks pour une session
  triggerFeedbacksForSession(salonId: string): Observable<any> {
    return this.http.post<any>(`${this.baseUrl}/trigger/${salonId}`, {});
  }

  // Récupérer les utilisateurs les mieux notés
  getTopRatedUsers(limit: number = 5): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/top-rated?limit=${limit}`);
  }
}
