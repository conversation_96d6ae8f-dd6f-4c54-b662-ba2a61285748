<div
  class="min-h-screen flex flex-col justify-between bg-gradient-to-br from-purple-700 via-pink-600 to-blue-500 text-white"
>
  <header class="p-6 flex items-center justify-between">
    <div
      class="min-h-screen flex flex-col justify-between bg-gradient-to-br from-purple-700 via-blue-600 to-purple-500 text-white"
    >
      <header class="p-6 flex items-center justify-between">
        <div class="text-2xl font-bold">{{ brandName }}</div>
        <nav class="space-x-4">
          <a routerLink="/login" class="hover:underline">Login</a>
          <a routerLink="/signUp" class="hover:underline">signUp</a>
        </nav>
      </header>

      <main
        class="flex-1 flex flex-col items-center justify-center text-center px-4"
      >
        <h1 class="text-4xl md:text-6xl font-extrabold mb-4">
          {{ brandName }}
        </h1>
        <p class="text-lg md:text-xl max-w-2xl mb-8">{{ tagline }}</p>
        <div class="grid gap-4 md:grid-cols-2 max-w-4xl w-full">
          <div
            *ngFor="let feature of features"
            class="bg-white/10 backdrop-blur-md p-4 rounded-xl shadow-xl"
          >
            <p class="text-base md:text-lg">✅ {{ feature }}</p>
          </div>
        </div>

        <!-- Ajout d'un bouton pour accéder à la page d'ajout de salon -->
        <div class="mt-10 space-x-4">
          <a
            routerLink="/salons/add"
            class="px-6 py-3 rounded-full bg-white text-purple-700 font-semibold hover:bg-gray-100 transition"
          >
            Ajouter un Salon
          </a>
        </div>

        <!-- Ajout d'un bouton pour accéder à la page d'ajout de session -->
        <div class="mt-10 space-x-4">
          <a
            routerLink="/sessions/add/exampleSalon"
            class="px-6 py-3 rounded-full bg-white text-purple-700 font-semibold hover:bg-gray-100 transition"
          >
            Ajouter une Session
          </a>
        </div>

        <div class="mt-10 space-x-4">
          <a
            routerLink="/singUp"
            class="px-6 py-3 rounded-full bg-white text-purple-700 font-semibold hover:bg-gray-100 transition"
            >Get Started</a
          >
          <a
            routerLink="/login"
            class="px-6 py-3 rounded-full border border-white text-white font-semibold hover:bg-white hover:text-purple-700 transition"
            >Login</a
          >
        </div>
      </main>

      <footer class="p-4 text-center text-sm">
        © {{ brandName }} 2025. All rights reserved.
      </footer>
    </div>
  </header>
</div>
