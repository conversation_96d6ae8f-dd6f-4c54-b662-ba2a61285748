<div class="container mt-5 p-4 rounded shadow-lg" style="max-width: 750px; background: #ffffff; color: #333333;">
  <!-- Titre pour la liste des salons -->
  <h2 class="text-center fw-bold mb-4" style="color: #4e54c8;">
    <i class="fas fa-list-alt me-2" style="color: #4e54c8;"></i> Liste des Salons avec leurs Sessions
  </h2>

  <!-- Barre de recherche -->
  <div class="mb-4 d-flex justify-content-between align-items-center">
    <input 
      type="text" 
      class="form-control me-2 border-0 shadow-sm"
      style="background-color: #f0f4ff; color: #4e54c8; border-radius: 8px;" 
      placeholder="Rechercher par nom..." 
      [(ngModel)]="searchTerm" 
      (input)="rechercherSalons()" 
      aria-label="Rechercher un salon par nom">
    <button 
      class="btn fw-bold d-flex align-items-center"
      style="background: linear-gradient(90deg, #4e54c8, #8f94fb); color: white; border-radius: 8px; transition: background 0.3s ease;" 
      (click)="rechercherSalons()" 
      aria-label="Lancer la recherche">
      <i class="fas fa-search me-2"></i> Rechercher
    </button>
  </div>

  <!-- Liste des salons -->
  <ul class="list-group mb-4">
    <li *ngFor="let salon of salons" class="list-group-item" style="background: #f7f8fc; border: none; border-radius: 8px; margin-bottom: 10px;">
      <!-- Informations sur le salon -->
      <div>
        <h5 class="fw-bold" style="color: #4e54c8;">
          <i class="fas fa-door-open me-2"></i>{{ salon.salon.nom }}
        </h5>
        <p style="font-size: 0.9rem; color: #555555;">{{ salon.salon.description }}</p>
      </div>

      <!-- Liste des sessions associées -->
      <div *ngIf="salon.sessions && salon.sessions.length > 0" class="mt-3">
        <h6 style="color: #4e54c8;">Sessions associées :</h6>
        <ul class="list-group">
          <li *ngFor="let session of salon.sessions" class="list-group-item" style="background: #ffffff; border: 1px solid #e0e0e0; border-radius: 6px; margin-bottom: 5px;">
            <p><strong>Type :</strong> {{ session.type }}</p>
            <p><strong>État :</strong> {{ session.etat }}</p>
            <p><strong>Date début :</strong> {{ session.dateDebut | date:'short' }}</p>
            <p><strong>Date fin :</strong> {{ session.dateFin | date:'short' }}</p>
            <p><strong>Créateur :</strong> {{ session.createurNom }}</p>
          </li>
        </ul>
      </div>

      <!-- Message si aucune session n'est associée -->
      <div *ngIf="!salon.sessions || salon.sessions.length === 0" class="mt-2">
        <p class="text-muted">Aucune session associée.</p>
      </div>
    </li>
  </ul>

  <!-- Message d'erreur si aucun salon trouvé -->
  <div *ngIf="salons.length === 0" class="alert text-center mt-3" style="background: #f7f8fc; color: #4e54c8; border-radius: 8px;">
    <i class="fas fa-exclamation-circle me-2"></i> Aucun salon trouvé.
  </div>
</div>
