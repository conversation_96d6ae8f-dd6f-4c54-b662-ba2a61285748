/* Animation pour l'apparition des résultats */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.result-container {
  animation: slideInUp 0.8s ease-out;
}

.stat-card {
  animation: fadeIn 1s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.stat-card:nth-child(2) {
  animation-delay: 0.4s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.6s;
}

.stat-card:nth-child(4) {
  animation-delay: 0.8s;
}

/* Animation pour le score principal */
.score-display {
  animation: scoreReveal 2s ease-out;
}

@keyframes scoreReveal {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Effet de pulsation pour les éléments importants */
.pulse-effect {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Animation pour les badges */
.badge-animation {
  animation: badgeAppear 1.5s ease-out;
}

@keyframes badgeAppear {
  0% {
    transform: scale(0) rotate(-360deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.3) rotate(-180deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Effet de confettis pour les certificats */
.certificate-celebration {
  position: relative;
  overflow: hidden;
}

.certificate-celebration::before {
  content: '🎉 🎊 ✨ 🌟 🎈 🎁 🏆 🥇';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 1.5rem;
  animation: confetti-rain 4s ease-in-out infinite;
  opacity: 0.9;
  z-index: 1;
}

@keyframes confetti-rain {
  0% {
    transform: translateY(-50px) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(300px) rotate(720deg);
    opacity: 0;
  }
}

/* Styles pour les cartes de questions */
.question-card {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.question-card.correct {
  border-left-color: #10b981;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
}

.question-card.incorrect {
  border-left-color: #ef4444;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05));
}

.question-card:hover {
  transform: translateX(5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Animation pour les boutons d'action */
.action-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.action-button:active {
  transform: translateY(-1px);
}

/* Effet de brillance pour les statistiques */
.stat-shine {
  position: relative;
  overflow: hidden;
}

.stat-shine::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shine-sweep 3s infinite;
}

@keyframes shine-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Styles pour les conseils d'amélioration */
.improvement-tip {
  transition: all 0.3s ease;
  cursor: pointer;
}

.improvement-tip:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.15);
}

/* Animation pour les icônes */
.icon-bounce {
  animation: iconBounce 2s infinite;
}

@keyframes iconBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -8px, 0);
  }
  90% {
    transform: translate3d(0, -3px, 0);
  }
}

/* Effet de rotation pour les icônes de difficulté */
.difficulty-icon {
  transition: transform 0.3s ease;
}

.difficulty-icon:hover {
  transform: rotate(360deg);
}

/* Styles pour les barres de progression */
.progress-bar {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  background-size: 200% 100%;
  animation: gradient-flow 3s ease infinite;
}

@keyframes gradient-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Effet de glow pour les éléments spéciaux */
.glow-effect {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.8);
}

/* Animation de compteur pour les statistiques */
.counter-animation {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .result-grid {
    grid-template-columns: 1fr;
  }
  
  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .score-display {
    font-size: 3rem;
  }
  
  .question-card {
    padding: 0.75rem;
  }
}

@media (max-width: 640px) {
  .action-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .certificate-celebration::before {
    font-size: 1rem;
  }
}

/* Effet de focus pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Animation de chargement */
.loading-dots {
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    color: rgba(255, 255, 255, 0.4);
  }
  50% {
    color: rgba(255, 255, 255, 1);
  }
  100% {
    color: rgba(255, 255, 255, 0.4);
  }
}

/* Styles pour les transitions fluides */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de survol pour les cartes */
.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}
