<div class="max-w-4xl mx-auto p-6">
  <div class="bg-white rounded-xl shadow-lg p-6">
    <h2 class="text-2xl font-bold text-purple-700 mb-6 text-center">Select a Top-Up Package</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div *ngFor="let pkg of packages" 
           [ngClass]="{'border-purple-500 shadow-md': selectedPackage?.id === pkg.id, 'border-gray-200': selectedPackage?.id !== pkg.id}"
           class="border-2 rounded-xl p-5 cursor-pointer transition-all hover:shadow-md relative"
           (click)="selectPackage(pkg)">
        
        <div *ngIf="pkg.isPopular" class="absolute -top-3 -right-3 bg-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full">
          POPULAR
        </div>
        
        <h3 class="text-xl font-bold text-gray-800 mb-2">{{ pkg.name }}</h3>
        <div class="flex justify-between items-baseline mb-4">
          <span class="text-gray-600">Price:</span>
          <span class="text-2xl font-bold text-purple-700">${{ pkg.amount }}</span>
        </div>
        
        <div class="flex justify-between items-baseline mb-4">
          <span class="text-gray-600">iMoney:</span>
          <span class="text-2xl font-bold text-cyan-500">{{ pkg.imoneyValue }}</span>
        </div>
        
        <p class="text-gray-500 text-sm mb-4">{{ pkg.description }}</p>
        
        <div *ngIf="pkg.imoneyValue > pkg.amount" class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-1 rounded-full text-center">
          {{ ((pkg.imoneyValue / pkg.amount - 1) * 100) | number:'1.0-0' }}% Bonus
        </div>
      </div>
    </div>
    
    <div class="flex flex-col space-y-4">
      <button 
        (click)="proceedToTopUp()" 
        [disabled]="!selectedPackage"
        [ngClass]="{'bg-purple-600 hover:bg-purple-700': selectedPackage, 'bg-gray-400 cursor-not-allowed': !selectedPackage}"
        class="w-full text-white py-3 px-4 rounded-md shadow transition">
        Proceed to Checkout
      </button>
      
      <button 
        (click)="goBackToWallets()" 
        class="w-full bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 px-4 rounded-md shadow transition">
        Back to Wallets
      </button>
    </div>
  </div>
</div>