import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';
import { CategoryWithChallenges } from '../../models/quiz.models';

@Component({
  selector: 'app-quiz-category-selection',
  templateUrl: './quiz-category-selection.component.html',
  styleUrls: ['./quiz-category-selection.component.css']
})
export class QuizCategorySelectionComponent implements OnInit {
  categories: CategoryWithChallenges[] = [];
  loading = true;
  errorMessage: string | null = null;
  currentUser: any;

  constructor(
    private quizService: QuizService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadCategories();
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    } else {
      this.errorMessage = 'Utilisateur non connecté.';
      this.router.navigate(['/login']);
    }
  }

  loadCategories(): void {
    this.loading = true;
    this.quizService.getCategoriesWithChallenges().subscribe({
      next: (response: any) => {
        console.log('Response from getCategoriesWithChallenges:', response);

        // Gérer le cas où la réponse est un objet avec une propriété contenant le tableau
        let categories = response;

        // Si la réponse est un objet avec une propriété 'data', 'categories', etc.
        if (response && typeof response === 'object' && !Array.isArray(response)) {
          if (response.data && Array.isArray(response.data)) {
            categories = response.data;
          } else if (response.categories && Array.isArray(response.categories)) {
            categories = response.categories;
          } else if (response.results && Array.isArray(response.results)) {
            categories = response.results;
          } else {
            // Si aucune propriété connue, essayer de convertir en tableau
            categories = [];
            console.warn('Format de réponse inattendu pour les catégories:', response);
          }
        }

        // S'assurer que categories est un tableau
        this.categories = Array.isArray(categories) ? categories : [];
        console.log('Categories loaded:', this.categories.length);
        console.log('First category:', this.categories[0]);
        this.cdr.detectChanges(); // Forcer la détection des changements
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des catégories:', error);
        this.errorMessage = 'Impossible de charger les catégories.';
        this.categories = []; // S'assurer que c'est un tableau vide en cas d'erreur
        this.loading = false;
      }
    });
  }

  selectCategory(category: CategoryWithChallenges): void {
    if (category.totalChallenges === 0) {
      this.errorMessage = 'Aucun challenge disponible dans cette catégorie.';
      return;
    }

    // Naviguer vers la liste des challenges de cette catégorie
    this.router.navigate(['/quiz/category', category._id], {
      state: { category: category }
    });
  }

  getCategoryProgress(category: CategoryWithChallenges): number {
    if (category.totalChallenges === 0) return 0;
    return Math.round(((category.completedChallenges || 0) / category.totalChallenges) * 100);
  }

  getCategoryIcon(categoryName: string): string {
    const icons: { [key: string]: string } = {
      'JavaScript': '🟨',
      'Python': '🐍',
      'Java': '☕',
      'Angular': '🅰️',
      'React': '⚛️',
      'Node.js': '🟢',
      'Database': '🗄️',
      'DevOps': '🔧',
      'Security': '🔒',
      'Mobile': '📱',
      'Web Development': '🌐',
      'Data Science': '📊',
      'Machine Learning': '🤖',
      'Cloud Computing': '☁️',
      'Cybersecurity': '🛡️'
    };
    return icons[categoryName] || '📚';
  }

  getDifficultyColor(averageScore?: number): string {
    if (!averageScore) return 'text-gray-500';
    if (averageScore >= 80) return 'text-green-500';
    if (averageScore >= 60) return 'text-yellow-500';
    return 'text-red-500';
  }

  getDifficultyText(averageScore?: number): string {
    if (!averageScore) return 'Non évalué';
    if (averageScore >= 80) return 'Facile';
    if (averageScore >= 60) return 'Moyen';
    return 'Difficile';
  }

  refreshCategories(): void {
    this.loadCategories();
  }

  goToProfile(): void {
    this.router.navigate(['/quiz/profile']);
  }

  goToLeaderboard(): void {
    this.router.navigate(['/quiz/leaderboard']);
  }
}
