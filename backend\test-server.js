const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Route de test simple
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Serveur backend fonctionnel',
    timestamp: new Date().toISOString()
  });
});

// Route pour tester les challenges (données mockées)
app.get('/api/quiz/challenges', (req, res) => {
  const mockChallenges = [
    {
      _id: '1',
      title: 'JavaScript Basics',
      description: 'Test your JavaScript knowledge',
      categoryId: 'cat1',
      categoryName: 'Programming',
      category: {
        _id: 'cat1',
        name: 'Programming',
        description: 'Programming challenges'
      },
      difficulty: 'easy',
      questions: [
        {
          _id: 'q1',
          question: 'What is JavaScript?',
          correct_answer: 'A programming language',
          incorrect_answers: ['A database', 'An operating system', 'A web browser']
        }
      ],
      totalQuestions: 1,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: '2',
      title: 'Python Fundamentals',
      description: 'Test your Python skills',
      categoryId: 'cat2',
      categoryName: 'Programming',
      category: {
        _id: 'cat2',
        name: 'Programming',
        description: 'Programming challenges'
      },
      difficulty: 'medium',
      questions: [
        {
          _id: 'q2',
          question: 'What is Python?',
          correct_answer: 'A programming language',
          incorrect_answers: ['A snake', 'A database', 'A framework']
        }
      ],
      totalQuestions: 1,
      timeLimit: 45,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
  
  res.json(mockChallenges);
});

// Route pour tester les catégories avec challenges
app.get('/api/quiz/categories-with-challenges', (req, res) => {
  const mockCategories = [
    {
      _id: 'cat1',
      name: 'Programming',
      description: 'Programming challenges and quizzes',
      challenges: [],
      totalChallenges: 2,
      completedChallenges: 0,
      averageScore: 0
    },
    {
      _id: 'cat2',
      name: 'Web Development',
      description: 'Web development challenges',
      challenges: [],
      totalChallenges: 1,
      completedChallenges: 0,
      averageScore: 0
    }
  ];
  
  res.json(mockCategories);
});

// Route pour tester les challenges par catégorie
app.get('/api/quiz/challenges/category/:categoryId', (req, res) => {
  const categoryId = req.params.categoryId;
  
  const mockChallenges = [
    {
      _id: '1',
      title: 'JavaScript Basics',
      description: 'Test your JavaScript knowledge',
      categoryId: categoryId,
      categoryName: 'Programming',
      category: {
        _id: categoryId,
        name: 'Programming',
        description: 'Programming challenges'
      },
      difficulty: 'easy',
      questions: [
        {
          _id: 'q1',
          question: 'What is JavaScript?',
          correct_answer: 'A programming language',
          incorrect_answers: ['A database', 'An operating system', 'A web browser']
        }
      ],
      totalQuestions: 1,
      timeLimit: 30,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
  
  res.json(mockChallenges);
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test démarré sur le port ${PORT}`);
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log(`🔗 Test challenges: http://localhost:${PORT}/api/quiz/challenges`);
  console.log(`🔗 Test categories: http://localhost:${PORT}/api/quiz/categories-with-challenges`);
});
