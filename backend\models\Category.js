const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  imageUrl: {
    type: String
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index pour optimiser les requêtes
CategorySchema.index({ name: 1 });
CategorySchema.index({ isActive: 1 });

// Middleware pour mettre à jour updatedAt
CategorySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Méthodes statiques
CategorySchema.statics.getActiveCategories = function() {
  return this.find({ isActive: true }).sort({ name: 1 });
};

CategorySchema.statics.getCategoriesWithChallenges = function() {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $lookup: {
        from: 'challenges',
        localField: '_id',
        foreignField: 'categoryId',
        as: 'challenges'
      }
    },
    {
      $addFields: {
        totalChallenges: { $size: '$challenges' },
        activeChallenges: {
          $size: {
            $filter: {
              input: '$challenges',
              cond: { $eq: ['$$this.isActive', true] }
            }
          }
        }
      }
    },
    {
      $sort: { name: 1 }
    }
  ]);
};

module.exports = mongoose.model('Category', CategorySchema);
