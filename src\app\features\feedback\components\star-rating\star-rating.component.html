<div class="star-rating-container" 
     (mouseleave)="onMouseLeave()">
  <span *ngFor="let star of stars" 
        [class]="getStarClass(star)"
        (click)="onStarClick(star)"
        (mouseenter)="onStarHover(star)"
        [title]="star + ' étoile' + (star > 1 ? 's' : '')">
    {{ getStarIcon(star) }}
  </span>
  
  <span *ngIf="rating > 0" class="rating-text">
    {{ rating }}/{{ maxStars }}
  </span>
</div>
