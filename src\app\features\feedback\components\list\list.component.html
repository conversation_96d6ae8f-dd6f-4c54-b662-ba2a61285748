<div class="container mt-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="text-primary">Liste des Feedbacks</h2>
      <a routerLink="/feedbacks/create" class="btn btn-success rounded-pill">
        <i class="bi bi-plus-circle me-1"></i><PERSON><PERSON><PERSON> un Feedback
      </a>
    </div>
  
    <div *ngFor="let feedback of feedbacks" class="card shadow-sm mb-4 rounded-4">
      <div class="card-body">
        <h5 class="card-title">
          <i class="bi bi-person-circle me-2"></i>Utilisateur : 
          <span class="text-muted">{{ feedback.userId }}</span>
        </h5>
  
        <p class="mb-1">
          <strong>Note :</strong>
          <span class="badge bg-success">{{ feedback.rating }} / 5</span>
        </p>
  
        <p class="mb-3">
          <strong>Commentaire :</strong>
          {{ feedback.comment || 'Aucun commentaire' }}
        </p>
  
        <div class="d-flex gap-2">
          <a [routerLink]="['/feedbacks/details', feedback._id]" class="btn btn-outline-primary btn-sm rounded-pill">
            <i class="bi bi-eye"></i> Voir
          </a>
  
          <a [routerLink]="['/feedbacks/update', feedback._id]" class="btn btn-outline-warning btn-sm rounded-pill">
            <i class="bi bi-pencil"></i> Modifier
          </a>
  
          <button (click)="deleteFeedback(feedback._id)" class="btn btn-outline-danger btn-sm rounded-pill">
            <i class="bi bi-trash"></i> Supprimer
          </button>
        </div>
      </div>
    </div>
  </div>
  