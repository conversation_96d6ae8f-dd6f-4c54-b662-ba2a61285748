import { Component, OnInit } from '@angular/core';
import { BadgeService } from '../../service/badge.service';

@Component({
  selector: 'app-badge-simple',
  template: `
    <div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
      <div class="max-w-7xl mx-auto">
        <h1 class="text-4xl font-bold text-white text-center mb-8">
          🏆 Mes Badges & Leaderboard
        </h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Section Badges -->
          <div class="lg:col-span-2">
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h2 class="text-2xl font-bold text-white mb-6">🎖️ Mes Badges</h2>
              
              <!-- Loading -->
              <div *ngIf="loading" class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
                <p class="text-white mt-4">Chargement des badges...</p>
              </div>
              
              <!-- Error -->
              <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500 text-red-100 p-4 rounded-lg mb-6">
                {{ errorMessage }}
              </div>
              
              <!-- Badges Grid -->
              <div *ngIf="!loading && !errorMessage" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div *ngFor="let badge of badges" 
                     class="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all">
                  <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                      <span class="text-2xl">🏆</span>
                    </div>
                    <div class="flex-1">
                      <h3 class="text-lg font-bold text-white">{{ badge.type }}</h3>
                      <p class="text-gray-300 text-sm">{{ badge.challengeTitle || 'Challenge' }}</p>
                      <p class="text-yellow-400 text-sm">Score: {{ badge.score }}/{{ badge.totalQuestions * 10 }}</p>
                      <p class="text-gray-400 text-xs">{{ badge.earnedAt | date:'short' }}</p>
                    </div>
                  </div>
                </div>
                
                <!-- Message si pas de badges -->
                <div *ngIf="badges.length === 0" class="col-span-2 text-center py-8">
                  <div class="text-6xl mb-4">🎯</div>
                  <p class="text-white text-lg">Aucun badge pour le moment</p>
                  <p class="text-gray-300">Passez des quiz pour gagner des badges !</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Section Leaderboard -->
          <div class="lg:col-span-1">
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
              <h2 class="text-2xl font-bold text-white mb-6">🏅 Classement</h2>
              
              <!-- Loading Leaderboard -->
              <div *ngIf="leaderboardLoading" class="text-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
              </div>
              
              <!-- Leaderboard -->
              <div *ngIf="!leaderboardLoading" class="space-y-3">
                <div *ngFor="let user of leaderboard; let i = index" 
                     class="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center"
                       [class]="i === 0 ? 'bg-yellow-500' : i === 1 ? 'bg-gray-400' : i === 2 ? 'bg-orange-600' : 'bg-gray-600'">
                    <span class="text-white font-bold text-sm">{{ i + 1 }}</span>
                  </div>
                  <div class="flex-1">
                    <p class="text-white font-medium text-sm">{{ user.userId?.fullName || 'Utilisateur' }}</p>
                    <p class="text-gray-300 text-xs">{{ user.totalScore }} points</p>
                  </div>
                </div>
                
                <!-- Message si pas de leaderboard -->
                <div *ngIf="leaderboard.length === 0" class="text-center py-4">
                  <p class="text-gray-300">Aucun classement disponible</p>
                </div>
              </div>
            </div>
            
            <!-- Statistiques personnelles -->
            <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20 mt-6">
              <h3 class="text-xl font-bold text-white mb-4">📊 Mes Stats</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-gray-300">Total Badges:</span>
                  <span class="text-white font-bold">{{ badges.length }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-300">Score Total:</span>
                  <span class="text-white font-bold">{{ totalScore }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-300">Rang:</span>
                  <span class="text-white font-bold">#{{ userRank || 'N/A' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .animate-spin {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  `]
})
export class BadgeSimpleComponent implements OnInit {
  badges: any[] = [];
  leaderboard: any[] = [];
  loading = true;
  leaderboardLoading = true;
  errorMessage: string | null = null;
  currentUser: any;
  totalScore = 0;
  userRank: number | null = null;

  constructor(private badgeService: BadgeService) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadBadges();
    this.loadLeaderboard();
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    }
  }

  loadBadges(): void {
    this.loading = true;
    this.badgeService.getAllBadges().subscribe({
      next: (badges) => {
        this.badges = badges;
        this.calculateTotalScore();
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des badges:', error);
        this.errorMessage = 'Impossible de charger les badges.';
        this.loading = false;
      }
    });
  }

  loadLeaderboard(): void {
    this.leaderboardLoading = true;
    // Simuler un leaderboard simple
    setTimeout(() => {
      this.leaderboard = [
        { userId: { fullName: 'Alice Martin' }, totalScore: 850 },
        { userId: { fullName: 'Bob Dupont' }, totalScore: 720 },
        { userId: { fullName: 'Claire Durand' }, totalScore: 680 },
        { userId: { fullName: this.currentUser?.fullName || 'Vous' }, totalScore: this.totalScore }
      ].sort((a, b) => b.totalScore - a.totalScore);
      
      this.calculateUserRank();
      this.leaderboardLoading = false;
    }, 1000);
  }

  calculateTotalScore(): void {
    this.totalScore = this.badges.reduce((sum, badge) => sum + (badge.score || 0), 0);
  }

  calculateUserRank(): void {
    if (this.currentUser) {
      const userIndex = this.leaderboard.findIndex(user => 
        user.userId.fullName === this.currentUser.fullName
      );
      this.userRank = userIndex >= 0 ? userIndex + 1 : null;
    }
  }
}
