<div class="container my-5 p-4 bg-white shadow rounded">
  <form [formGroup]="challengeForm" (ngSubmit)="onSubmit()" class="needs-validation">

    <!-- Title -->
    <div class="mb-3">
      <label for="title" class="form-label">Title:</label>
      <input 
        id="title" 
        type="text" 
        formControlName="title" 
        class="form-control" 
        placeholder="Enter challenge title"
        required
      >
    </div>
    
    <!-- Description -->
    <div class="mb-3">
      <label for="description" class="form-label">Description:</label>
      <textarea 
        id="description" 
        formControlName="description" 
        class="form-control" 
        rows="4"
        placeholder="Enter a description"
        required
      ></textarea>
    </div>
    
    <!-- Skill -->
    <div class="mb-3">
      <label for="skill" class="form-label">Skill:</label>
      <input 
        id="skill" 
        type="text" 
        formControlName="skill" 
        class="form-control" 
        placeholder="Enter required skill"
        required
      >
    </div>
    
    <!-- User ID -->
    <div class="mb-3">
      <label for="createdBy" class="form-label">Created By:</label>
      <input 
        id="createdBy" 
        type="text" 
        formControlName="createdBy" 
        class="form-control" 
        placeholder="Enter here"
        required
      >
    </div>
    
    <!-- Difficulty -->
    <div class="mb-3">
      <label for="difficulty" class="form-label">Difficulty:</label>
      <select 
        id="difficulty" 
        formControlName="difficulty" 
        class="form-select"
        required
      >
        <option value="" disabled selected>Select difficulty</option>
        <option value="easy">Easy</option>
        <option value="medium">Medium</option>
        <option value="hard">Hard</option>
      </select>
    </div>
    
    <!-- Start Date -->
    <div class="mb-3">
      <label for="startDate" class="form-label">Start Date:</label>
      <input 
        id="startDate" 
        type="date" 
        formControlName="startDate" 
        class="form-control"
        required
      >
    </div>

    <!-- Trivia Settings -->
    <div formGroupName="triviaSettings" class="mb-4 border p-3 rounded bg-light">
      <h5>
        <input type="checkbox" formControlName="enabled" id="enableTrivia" />
        <label for="enableTrivia">Associer un Quiz Trivia</label>
      </h5>

      <div *ngIf="challengeForm.get('triviaSettings.enabled')?.value">
        <div class="mb-3">
          <label for="amount" class="form-label">Nombre de questions :</label>
          <input id="amount" type="number" formControlName="amount" class="form-control" min="1" max="50" />
        </div>

        <div class="mb-3">
          <label for="category" class="form-label">Catégorie :</label>
          <select id="category" formControlName="category" class="form-select">
            <option value="">Toutes</option>
            <option *ngFor="let cat of categoriesTrivia" [value]="cat.id">{{ cat.name }}</option>
          </select>
        </div>

        <div class="mb-3">
          <label for="difficulty" class="form-label">Difficulté :</label>
          <select id="difficulty" formControlName="difficulty" class="form-select">
            <option value="easy">Facile</option>
            <option value="medium">Moyen</option>
            <option value="hard">Difficile</option>
          </select>
        </div>

        <div class="mb-3">
          <label for="type" class="form-label">Type :</label>
          <select id="type" formControlName="type" class="form-select">
            <option value="multiple">Choix multiple</option>
            <option value="boolean">Vrai/Faux</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="d-grid">
      <button 
        type="submit" 
        class="btn btn-primary"
      >
        Save
      </button>
    </div>
  </form>
</div>
