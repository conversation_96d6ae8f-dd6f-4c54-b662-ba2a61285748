import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FeedbackService } from '../../services/feedback.service';
import { Feedback } from '../../models/feedback.models';

@Component({
  selector: 'app-feedback-modal',
  templateUrl: './feedback-modal.component.html',
  styleUrls: ['./feedback-modal.component.css']
})
export class FeedbackModalComponent implements OnInit {
  @Input() isVisible: boolean = false;
  @Input() targetUser: any = null;
  @Input() salonId: string = '';
  @Input() sessionStartTime: Date = new Date();
  @Output() close = new EventEmitter<void>();
  @Output() feedbackSubmitted = new EventEmitter<Feedback>();

  feedbackForm: FormGroup;
  isSubmitting: boolean = false;
  errorMessage: string = '';
  successMessage: string = '';

  constructor(
    private fb: FormBuilder,
    private feedbackService: FeedbackService
  ) {
    this.feedbackForm = this.fb.group({
      rating: [0, [Validators.required, Validators.min(1), Validators.max(5)]],
      comment: ['', [Validators.maxLength(500)]]
    });
  }

  ngOnInit(): void {
    // Reset form when modal opens
    if (this.isVisible) {
      this.resetForm();
    }
  }

  ngOnChanges(): void {
    if (this.isVisible) {
      this.resetForm();
    }
  }

  resetForm(): void {
    this.feedbackForm.reset({
      rating: 0,
      comment: ''
    });
    this.errorMessage = '';
    this.successMessage = '';
    this.isSubmitting = false;
  }

  onSubmit(): void {
    if (this.feedbackForm.invalid || this.isSubmitting) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const feedbackData = {
      targetUserId: this.targetUser._id,
      salonId: this.salonId,
      rating: this.feedbackForm.value.rating,
      comment: this.feedbackForm.value.comment,
      sessionStartTime: this.sessionStartTime
    };

    this.feedbackService.createFeedback(feedbackData).subscribe({
      next: (feedback) => {
        this.successMessage = 'Feedback envoyé avec succès !';
        this.feedbackSubmitted.emit(feedback);
        
        // Fermer le modal après 2 secondes
        setTimeout(() => {
          this.onClose();
        }, 2000);
      },
      error: (error) => {
        console.error('Erreur lors de l\'envoi du feedback:', error);
        this.errorMessage = error.error?.error || 'Erreur lors de l\'envoi du feedback';
        this.isSubmitting = false;
      }
    });
  }

  onClose(): void {
    this.resetForm();
    this.close.emit();
  }

  onBackdropClick(event: Event): void {
    if (event.target === event.currentTarget) {
      this.onClose();
    }
  }

  getRatingText(rating: number): string {
    const ratingTexts = {
      1: 'Très insatisfait',
      2: 'Insatisfait',
      3: 'Neutre',
      4: 'Satisfait',
      5: 'Très satisfait'
    };
    return ratingTexts[rating as keyof typeof ratingTexts] || '';
  }

  get rating() {
    return this.feedbackForm.get('rating');
  }

  get comment() {
    return this.feedbackForm.get('comment');
  }
}
