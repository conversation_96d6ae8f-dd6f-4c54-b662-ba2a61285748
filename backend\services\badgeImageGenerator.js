const fs = require('fs');
const path = require('path');

class BadgeImageGenerator {
  constructor() {
    this.badgeTemplates = {
      PARTICIPANT: {
        color: '#4CAF50',
        icon: '🏅',
        title: 'Participant',
        description: 'Félicitations pour votre participation!'
      },
      BEGINNER: {
        color: '#2196F3',
        icon: '🌟',
        title: 'Débutant',
        description: 'Premier pas vers l\'excellence!'
      },
      INTERMEDIATE: {
        color: '#FF9800',
        icon: '🏆',
        title: 'Intermédiaire',
        description: 'Compétences en développement!'
      },
      EXPERT: {
        color: '#9C27B0',
        icon: '👑',
        title: 'Expert',
        description: '<PERSON><PERSON><PERSON><PERSON> exceptionnelle!'
      },
      MASTER: {
        color: '#F44336',
        icon: '💎',
        title: '<PERSON><PERSON><PERSON>',
        description: 'Excellence absolue!'
      }
    };

    this.certificateTemplate = {
      color: '#1976D2',
      icon: '📜',
      title: 'Certificat de Compétence',
      description: 'Certification officielle SkillsHub'
    };
  }

  // Génère une URL d'image de badge en utilisant un service externe
  generateBadgeImage(badgeType, userName, categoryName, percentage) {
    const template = this.badgeTemplates[badgeType] || this.badgeTemplates.PARTICIPANT;
    
    // Utilisation de l'API Shields.io pour générer des badges professionnels
    const badgeUrl = `https://img.shields.io/badge/${encodeURIComponent(template.title)}-${percentage}%25-${template.color.replace('#', '')}.svg?style=for-the-badge&logo=data:image/svg+xml;base64,${this.getIconBase64(template.icon)}`;
    
    return badgeUrl;
  }

  // Génère une URL d'image de certificat
  generateCertificateImage(userName, totalScore, categoryName) {
    // Utilisation d'un service de génération d'images dynamiques
    const certificateUrl = `https://via.placeholder.com/800x600/${this.certificateTemplate.color.replace('#', '')}/FFFFFF?text=${encodeURIComponent(`Certificat de ${userName} - ${totalScore} points - ${categoryName}`)}`;
    
    return certificateUrl;
  }

  // Génère une image SVG personnalisée pour les badges
  generateCustomBadgeSVG(badgeType, userName, categoryName, percentage) {
    const template = this.badgeTemplates[badgeType] || this.badgeTemplates.PARTICIPANT;
    
    const svg = `
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${template.color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${this.darkenColor(template.color, 20)};stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <!-- Badge circle -->
        <circle cx="100" cy="100" r="90" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
        
        <!-- Icon -->
        <text x="100" y="80" text-anchor="middle" font-size="40" fill="white">${template.icon}</text>
        
        <!-- Badge type -->
        <text x="100" y="110" text-anchor="middle" font-size="16" font-weight="bold" fill="white">${template.title}</text>
        
        <!-- Percentage -->
        <text x="100" y="130" text-anchor="middle" font-size="14" fill="white">${percentage}%</text>
        
        <!-- Category -->
        <text x="100" y="150" text-anchor="middle" font-size="12" fill="white">${categoryName}</text>
        
        <!-- Border decoration -->
        <circle cx="100" cy="100" r="85" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2" stroke-dasharray="5,5"/>
      </svg>
    `;
    
    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
  }

  // Génère une image SVG personnalisée pour les certificats
  generateCustomCertificateSVG(userName, totalScore, achievements) {
    const svg = `
      <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="certGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#1976D2;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#0D47A1;stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <!-- Background -->
        <rect width="800" height="600" fill="url(#certGrad)"/>
        
        <!-- Border -->
        <rect x="20" y="20" width="760" height="560" fill="none" stroke="#FFD700" stroke-width="8"/>
        <rect x="40" y="40" width="720" height="520" fill="none" stroke="#FFD700" stroke-width="2"/>
        
        <!-- Header -->
        <text x="400" y="100" text-anchor="middle" font-size="36" font-weight="bold" fill="#FFD700">CERTIFICAT DE COMPÉTENCE</text>
        <text x="400" y="130" text-anchor="middle" font-size="18" fill="white">SkillsHub - Plateforme d'Excellence</text>
        
        <!-- Icon -->
        <text x="400" y="180" text-anchor="middle" font-size="60" fill="#FFD700">🏆</text>
        
        <!-- Main content -->
        <text x="400" y="240" text-anchor="middle" font-size="24" fill="white">Ceci certifie que</text>
        <text x="400" y="280" text-anchor="middle" font-size="32" font-weight="bold" fill="#FFD700">${userName}</text>
        <text x="400" y="320" text-anchor="middle" font-size="20" fill="white">a obtenu un score total de</text>
        <text x="400" y="360" text-anchor="middle" font-size="36" font-weight="bold" fill="#FFD700">${totalScore} points</text>
        <text x="400" y="400" text-anchor="middle" font-size="18" fill="white">démontrant une maîtrise exceptionnelle</text>
        <text x="400" y="425" text-anchor="middle" font-size="18" fill="white">des compétences évaluées</text>
        
        <!-- Date -->
        <text x="400" y="480" text-anchor="middle" font-size="16" fill="white">Délivré le ${new Date().toLocaleDateString('fr-FR')}</text>
        
        <!-- Signature area -->
        <text x="200" y="530" text-anchor="middle" font-size="14" fill="white">Directeur SkillsHub</text>
        <line x1="120" y1="540" x2="280" y2="540" stroke="white" stroke-width="1"/>
        
        <text x="600" y="530" text-anchor="middle" font-size="14" fill="white">Certification ID: ${this.generateCertificationId()}</text>
      </svg>
    `;
    
    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
  }

  // Utilitaires
  darkenColor(color, percent) {
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  }

  getIconBase64(icon) {
    return Buffer.from(icon).toString('base64');
  }

  generateCertificationId() {
    return 'CERT-' + Date.now().toString(36).toUpperCase() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();
  }

  // Détermine le type de badge basé sur le pourcentage
  getBadgeType(percentage) {
    if (percentage >= 95) return 'MASTER';
    if (percentage >= 85) return 'EXPERT';
    if (percentage >= 70) return 'INTERMEDIATE';
    if (percentage >= 50) return 'BEGINNER';
    return 'PARTICIPANT';
  }
}

module.exports = new BadgeImageGenerator();
