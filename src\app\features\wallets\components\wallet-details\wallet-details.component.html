<div *ngIf="isLoading$ | async" class="flex justify-center items-center h-64">
  <div
    class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"
  ></div>
</div>

<div *ngIf="wallet$ | async as wallet" class="p-6 max-w-lg mx-auto">
  <div
    class="bg-gradient-to-r from-purple-700 to-indigo-900 text-white p-6 rounded-xl shadow-lg"
  >
    <h2 class="text-2xl font-bold text-pink-400 mb-4">My Wallet</h2>

    <div *ngIf="wallet?.imoney" class="space-y-4">
      <div class="flex justify-between items-center">
        <span class="text-gray-300">Balance:</span>
        <span class="text-3xl font-bold text-cyan-400">{{
          wallet.imoney?.value
        }}</span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-gray-300">Currency:</span>
        <span class="font-medium">{{ wallet.imoney?.currencyType }}</span>
      </div>

      <div class="flex justify-between items-center">
        <span class="text-gray-300">Status:</span>
        <span
          [class.text-green-400]="wallet.isActive"
          [class.text-red-400]="!wallet.isActive"
          class="font-medium"
        >
          {{ wallet.isActive ? "Active" : "Deactivated" }}
        </span>
      </div>

      <div
        *ngIf="wallet.deactivatedAt && !wallet.isActive"
        class="flex justify-between items-center"
      >
        <span class="text-gray-300">Deactivated:</span>
        <span class="font-medium">{{
          wallet.deactivatedAt | date : "medium"
        }}</span>
      </div>

      <div *ngIf="wallet.user" class="flex justify-between items-center">
        <span class="text-gray-300">Owner:</span>
        <span class="font-medium">{{
          wallet.user?.fullName || "Unknown"
        }}</span>
      </div>
    </div>

    <div *ngIf="!wallet?.imoney" class="text-gray-400 py-4">
      No balance information available
    </div>

    <div class="mt-6 flex space-x-4">
      <a
        [routerLink]="wallet.isActive ? '/wallets/packages' : null"
        [class.cursor-not-allowed]="!wallet.isActive"
        [class.opacity-50]="!wallet.isActive"
        [class.bg-gray-500]="!wallet.isActive"
        [class.hover:bg-gray-500]="!wallet.isActive"
        class="bg-pink-600 hover:bg-pink-700 text-white py-2 px-4 rounded-lg transition"
        (click)="$event.preventDefault(); handleTopUpClick(wallet)"
      >
        Top Up
      </a>
      <button
        (click)="goBackToWallets()"
        class="bg-gray-700 hover:bg-gray-800 text-white py-2 px-4 rounded-lg transition"
      >
        Back to Wallets
      </button>
    </div>
  </div>

  <div class="mt-4">
    <button
      (click)="toggleWalletStatus(wallet)"
      [disabled]="isToggling || (!wallet.isActive && !canActivate)"
      [class.bg-red-600]="wallet.isActive"
      [class.hover:bg-red-700]="wallet.isActive"
      [class.bg-green-600]="!wallet.isActive && canActivate"
      [class.hover:bg-green-700]="!wallet.isActive && canActivate"
      [class.bg-gray-500]="!wallet.isActive && !canActivate"
      [class.hover:bg-gray-500]="!wallet.isActive && !canActivate"
      class="w-full text-white py-2 px-4 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <span *ngIf="isToggling" class="inline-block mr-2">
        <svg
          class="animate-spin h-4 w-4 text-white"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </span>
      {{ wallet.isActive ? "Deactivate Wallet" : "Activate Wallet" }}
    </button>

    <div *ngIf="statusError" class="mt-2 text-red-500 text-sm">
      {{ statusError }}
    </div>

    <div *ngIf="statusSuccess" class="mt-2 text-green-500 text-sm">
      {{ statusSuccess }}
    </div>

    <div
      *ngIf="!wallet.isActive && timeUntilActivation"
      class="mt-2 text-amber-500 text-sm"
    >
      Note: You can only activate a wallet after 48 hours of deactivation. Time
      remaining: {{ timeUntilActivation }}.
    </div>

    <div
      *ngIf="!wallet.isActive && canActivate"
      class="mt-2 text-green-500 text-sm"
    >
      Your wallet is now eligible for activation.
    </div>
  </div>

  <div class="mt-6">
    <div
      class="bg-gradient-to-r from-purple-700 to-indigo-900 text-white p-6 rounded-xl shadow-lg"
    >
      <h3 class="text-xl font-bold text-pink-400 mb-4">Transaction History</h3>
      <app-transaction-history [wallet]="wallet"></app-transaction-history>
    </div>
  </div>

  <div class="mt-6">
    <div
      class="bg-gradient-to-r from-purple-700 to-indigo-900 text-white p-6 rounded-xl shadow-lg"
    >
      <h3 class="text-xl font-bold text-pink-400 mb-4">Wallet Analytics</h3>
      <app-wallet-analytics [wallet]="wallet"></app-wallet-analytics>
    </div>
  </div>
</div>
