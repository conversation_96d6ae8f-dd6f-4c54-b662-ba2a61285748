const mongoose = require('mongoose');

const FeedbackSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  targetUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  salonId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Salon',
    required: true
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  comment: {
    type: String,
    trim: true,
    maxlength: 500
  },
  sessionStartTime: {
    type: Date,
    required: true
  },
  feedbackTriggeredAt: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index pour optimiser les requêtes
FeedbackSchema.index({ userId: 1, salonId: 1 });
FeedbackSchema.index({ targetUserId: 1 });
FeedbackSchema.index({ salonId: 1, createdAt: -1 });
FeedbackSchema.index({ sessionStartTime: 1 });

// Middleware pour mettre à jour updatedAt
FeedbackSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Méthodes statiques
FeedbackSchema.statics.getFeedbacksForSalon = function(salonId) {
  return this.find({ salonId: salonId, isActive: true })
    .populate('userId', 'fullName email')
    .populate('targetUserId', 'fullName email')
    .populate('salonId', 'nom description')
    .sort({ createdAt: -1 });
};

FeedbackSchema.statics.getUserFeedbacks = function(userId) {
  return this.find({ 
    $or: [{ userId: userId }, { targetUserId: userId }],
    isActive: true 
  })
    .populate('userId', 'fullName email')
    .populate('targetUserId', 'fullName email')
    .populate('salonId', 'nom description')
    .sort({ createdAt: -1 });
};

FeedbackSchema.statics.getAverageRatingForUser = async function(userId) {
  const result = await this.aggregate([
    { $match: { targetUserId: new mongoose.Types.ObjectId(userId), isActive: true } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalFeedbacks: { $sum: 1 }
      }
    }
  ]);
  
  return result.length > 0 ? result[0] : { averageRating: 0, totalFeedbacks: 0 };
};

// Méthode pour vérifier si un feedback peut être créé
FeedbackSchema.statics.canCreateFeedback = async function(userId, targetUserId, salonId, sessionStartTime) {
  // Vérifier si 5 minutes se sont écoulées depuis le début de la session
  const fiveMinutesAgo = new Date(sessionStartTime.getTime() + 5 * 60 * 1000);
  const now = new Date();
  
  if (now < fiveMinutesAgo) {
    return { canCreate: false, reason: 'Il faut attendre 5 minutes après le début de la session' };
  }
  
  // Vérifier si un feedback existe déjà pour cette session
  const existingFeedback = await this.findOne({
    userId: userId,
    targetUserId: targetUserId,
    salonId: salonId,
    sessionStartTime: sessionStartTime,
    isActive: true
  });
  
  if (existingFeedback) {
    return { canCreate: false, reason: 'Un feedback a déjà été donné pour cette session' };
  }
  
  return { canCreate: true };
};

// Méthode pour déclencher automatiquement les feedbacks
FeedbackSchema.statics.triggerFeedbacksForSession = async function(salonId, participants, sessionStartTime) {
  try {
    const feedbackPrompts = [];
    
    // Créer des invitations de feedback entre tous les participants
    for (let i = 0; i < participants.length; i++) {
      for (let j = 0; j < participants.length; j++) {
        if (i !== j) {
          const canCreate = await this.canCreateFeedback(
            participants[i]._id,
            participants[j]._id,
            salonId,
            sessionStartTime
          );
          
          if (canCreate.canCreate) {
            feedbackPrompts.push({
              fromUser: participants[i],
              toUser: participants[j],
              salonId: salonId,
              sessionStartTime: sessionStartTime
            });
          }
        }
      }
    }
    
    return feedbackPrompts;
  } catch (error) {
    console.error('Erreur lors du déclenchement des feedbacks:', error);
    throw error;
  }
};

module.exports = mongoose.model('Feedback', FeedbackSchema);
