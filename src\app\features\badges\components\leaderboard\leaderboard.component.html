<div class="container mt-4 leaderboard-container">
  <h2 class="text-center mb-4 leaderboard-title">🏆 Classement des Utilisateurs</h2>

  <!-- Message d'erreur -->
  <div *ngIf="errorMessage" class="alert alert-danger">
    {{ errorMessage }}
  </div>

  <!-- Leaderboard -->
  <div *ngIf="leaderboard && leaderboard.length > 0">
    <div class="table-responsive leaderboard-card shadow-lg">
      <table class="table table-hover leaderboard-table">
        <thead class="leaderboard-header">
          <tr>
            <th scope="col">Position</th>
            <th scope="col">Utilisateur</th>
            <th scope="col">Score</th>
            <th scope="col">Nombre de Badges</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let user of leaderboard; index as i">
            <th scope="row">{{ i + 1 }}</th>
            <td>{{ user.userId }}</td>
            <td>{{ user.score }}</td>
            <td>{{ user.badgeCount }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Message si aucun utilisateur -->
  <div *ngIf="leaderboard && leaderboard.length === 0" class="alert alert-info text-center mt-4">
    Le leaderboard est vide pour le moment.
  </div>
</div>
