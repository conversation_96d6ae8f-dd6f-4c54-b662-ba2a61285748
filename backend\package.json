{"name": "skillshub-backend", "version": "1.0.0", "description": "Backend API pour SkillsHub avec système de quiz", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node test-data.js"}, "keywords": ["nodejs", "express", "mongodb", "quiz", "api"], "author": "SkillsHub Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "body-parser": "^1.20.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}