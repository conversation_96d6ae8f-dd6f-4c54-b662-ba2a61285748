{"name": "skillshub-backend", "version": "1.0.0", "description": "Backend API pour SkillsHub avec système de quiz", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node test-data.js"}, "keywords": ["nodejs", "express", "mongodb", "quiz", "api"], "author": "SkillsHub Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0"}, "devDependencies": {"autoprefixer": "^10.4.21", "nodemon": "^3.0.1", "postcss": "^8.5.3", "tailwindcss": "^4.1.7"}, "engines": {"node": ">=14.0.0"}}