import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

//import { QuizRoutingModule } from './quiz-routing.module';
import { QuizCategorySelectionComponent } from './components/quiz-category-selection/quiz-category-selection.component';
import { QuizByCategoryComponent } from './components/quiz-by-category/quiz-by-category.component';
import { QuizPlayerComponent } from './components/quiz-player/quiz-player.component';
import { QuizResultsComponent } from './components/quiz-results/quiz-results.component';
import { QuizProfileComponent } from './components/quiz-profile/quiz-profile.component';
import { QuizLeaderboardComponent } from './components/quiz-leaderboard/quiz-leaderboard.component';
import { QuizRoutingModule } from './quiz-routing.module';

@NgModule({
  declarations: [
    QuizCategorySelectionComponent,
    QuizByCategoryComponent,
    QuizPlayerComponent,
    QuizResultsComponent,
    QuizProfileComponent,
    QuizLeaderboardComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    QuizRoutingModule
  ],
  exports: [
    QuizCategorySelectionComponent,
    QuizByCategoryComponent,
    QuizPlayerComponent,
    QuizResultsComponent,
    QuizProfileComponent,
    QuizLeaderboardComponent
  ]
})
export class QuizModule { }



