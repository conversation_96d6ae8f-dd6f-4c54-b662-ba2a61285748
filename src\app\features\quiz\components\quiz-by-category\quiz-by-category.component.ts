import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';
import { QuizChallenge, CategoryWithChallenges } from '../../models/quiz.models';

@Component({
  selector: 'app-quiz-by-category',
  templateUrl: './quiz-by-category.component.html',
  styleUrls: ['./quiz-by-category.component.css']
})
export class QuizByCategoryComponent implements OnInit {
  categoryId!: string;
  category: CategoryWithChallenges | null = null;
  challenges: QuizChallenge[] = [];
  loading = true;
  errorMessage: string | null = null;
  currentUser: any;
  selectedDifficulty: string = 'all';
  searchTerm: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private quizService: QuizService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.categoryId = this.route.snapshot.paramMap.get('id') || '';

    // Récupérer la catégorie depuis l'état de navigation
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state?.['category']) {
      this.category = navigation.extras.state['category'];
    }

    this.loadChallenges();
  }

  loadCurrentUser(): void {
    const user = localStorage.getItem('user');
    if (user) {
      this.currentUser = JSON.parse(user);
    } else {
      this.errorMessage = 'Utilisateur non connecté.';
      this.router.navigate(['/login']);
    }
  }

  loadChallenges(): void {
    this.loading = true;
    this.quizService.getChallengesByCategory(this.categoryId).subscribe({
      next: (challenges) => {
        this.challenges = challenges;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des challenges:', error);
        this.errorMessage = 'Impossible de charger les challenges.';
        this.loading = false;
      }
    });
  }

  get filteredChallenges(): QuizChallenge[] {
    return this.challenges.filter(challenge => {
      const matchesDifficulty = this.selectedDifficulty === 'all' || challenge.difficulty === this.selectedDifficulty;
      const matchesSearch = challenge.title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           challenge.description.toLowerCase().includes(this.searchTerm.toLowerCase());
      return matchesDifficulty && matchesSearch && challenge.isActive;
    });
  }

  startQuiz(challenge: QuizChallenge): void {
    if (!challenge.isActive) {
      this.errorMessage = 'Ce challenge n\'est pas disponible actuellement.';
      return;
    }

    if (challenge.questions.length === 0) {
      this.errorMessage = 'Ce challenge ne contient aucune question.';
      return;
    }

    // Sauvegarder le challenge sélectionné
    localStorage.setItem('selectedChallenge', JSON.stringify(challenge));

    // Naviguer vers le composant de quiz
    this.router.navigate(['/quiz/play', challenge._id]);
  }

  getDifficultyColor(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'hard': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }

  getDifficultyBadgeClass(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  getDifficultyIcon(difficulty: string): string {
    switch (difficulty) {
      case 'easy': return '🟢';
      case 'medium': return '🟡';
      case 'hard': return '🔴';
      default: return '⚪';
    }
  }

  getEstimatedTime(totalQuestions: number): string {
    const minutesPerQuestion = 1.5;
    const totalMinutes = Math.ceil(totalQuestions * minutesPerQuestion);
    return `${totalMinutes} min`;
  }

  goBack(): void {
    this.router.navigate(['/quiz/categories']);
  }



  clearFilters(): void {
    this.selectedDifficulty = 'all';
    this.searchTerm = '';
  }

  refreshChallenges(): void {
    this.loadChallenges();
  }

  getChallengeIcon(title: string): string {
    const titleLower = title.toLowerCase();
    if (titleLower.includes('javascript')) return '🟨';
    if (titleLower.includes('python')) return '🐍';
    if (titleLower.includes('java')) return '☕';
    if (titleLower.includes('angular')) return '🅰️';
    if (titleLower.includes('react')) return '⚛️';
    if (titleLower.includes('node')) return '🟢';
    if (titleLower.includes('database') || titleLower.includes('sql')) return '🗄️';
    if (titleLower.includes('security')) return '🔒';
    if (titleLower.includes('mobile')) return '📱';
    if (titleLower.includes('web')) return '🌐';
    return '🧠';
  }
}
