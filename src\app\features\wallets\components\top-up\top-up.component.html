<div class="max-w-md mx-auto p-6">
  <div class="bg-white rounded-xl shadow-lg p-6">
    <h2 class="text-2xl font-bold text-purple-700 mb-6">Checkout</h2>
    
    <div *ngIf="loading$ | async" class="flex justify-center my-4">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
    </div>
    
    <div *ngIf="walletError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      {{ walletError }}
    </div>
    
    <div *ngIf="selectedPackage && !walletError" class="space-y-6">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ selectedPackage.name }} Package</h3>
        
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="text-gray-600">Amount:</span>
            <span class="font-medium">${{ selectedPackage.amount }}</span>
          </div>
          
          <div class="flex justify-between">
            <span class="text-gray-600">iMoney Value:</span>
            <span class="font-medium text-cyan-600">{{ selectedPackage.imoneyValue }}</span>
          </div>
          
          <div *ngIf="selectedPackage.imoneyValue > selectedPackage.amount" class="flex justify-between">
            <span class="text-gray-600">Bonus:</span>
            <span class="font-medium text-green-600">
              {{ ((selectedPackage.imoneyValue / selectedPackage.amount - 1) * 100) | number:'1.0-0' }}%
            </span>
          </div>
        </div>
      </div>
      
      <div *ngIf="error$ | async as error" class="text-red-500 text-sm mt-2">
        {{ error }}
      </div>
      
      <div class="flex space-x-4">
        <button
          (click)="goBackToPackages()"
          class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 px-4 rounded-md shadow transition"
        >
          Change Package
        </button>
        
        <button
          (click)="initiateCheckout()"
          [disabled]="walletError !== null"
          [ngClass]="{'bg-purple-600 hover:bg-purple-700': !walletError, 'bg-gray-400 cursor-not-allowed': walletError}"
          class="flex-1 text-white py-3 px-4 rounded-md shadow transition"
        >
          <span *ngIf="loading$ | async" class="inline-block mr-2">
            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          Pay Now
        </button>
      </div>
    </div>
  </div>
</div>
