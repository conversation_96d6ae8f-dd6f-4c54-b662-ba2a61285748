/* Styles pour la limitation de lignes */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Animation pour les cartes de challenge */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.challenge-card {
  animation: slideInUp 0.6s ease-out;
  position: relative;
  overflow: hidden;
}

.challenge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.challenge-card:hover::before {
  left: 100%;
}

/* Styles pour les badges de difficulté */
.difficulty-badge {
  position: relative;
  overflow: hidden;
}

.difficulty-badge::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.difficulty-badge:hover::after {
  transform: translateX(100%);
}

/* Animation pour les icônes */
.icon-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Effet de pulsation pour les éléments actifs */
.pulse-active {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);
  }
}

/* Styles pour les filtres */
.filter-container {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-input:focus {
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
}

/* Styles pour les boutons */
.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-gradient:active {
  transform: translateY(0);
}

/* Styles pour les statistiques */
.stat-item {
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: scale(1.05);
  color: #a78bfa;
}

/* Effet de brillance sur les cartes */
.card-shine {
  position: relative;
  overflow: hidden;
}

.card-shine::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: rotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s;
}

.card-shine:hover::after {
  opacity: 1;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

/* Styles pour les indicateurs de statut */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  background: currentColor;
  border-radius: 2px;
}

/* Animation pour le loading */
.loading-spinner {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 3px solid #ffffff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .challenge-card {
    margin-bottom: 1rem;
  }
  
  .filter-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .btn-responsive {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 640px) {
  .text-responsive {
    font-size: 1.25rem;
  }
  
  .grid-responsive {
    grid-template-columns: 1fr;
  }
  
  .p-responsive {
    padding: 1rem;
  }
}

/* Effet de focus pour l'accessibilité */
.focus-visible:focus {
  outline: 2px solid #a78bfa;
  outline-offset: 2px;
}

/* Styles pour les transitions fluides */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Effet de survol pour les éléments interactifs */
.interactive-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

