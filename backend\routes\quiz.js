const express = require('express');
const router = express.Router();
const Category = require('../models/Category');
const Challenge = require('../models/Challenge');
const QuizScore = require('../models/QuizScore');
const UserProgress = require('../models/UserProgress');
const Badge = require('../models/Badge');
const mongoose = require('mongoose');

// GET /api/quiz/categories-with-challenges
router.get('/categories-with-challenges', async (req, res) => {
  try {
    const categories = await Category.aggregate([
      {
        $lookup: {
          from: 'challenges',
          localField: '_id',
          foreignField: 'categoryId',
          as: 'challenges'
        }
      },
      {
        $addFields: {
          totalChallenges: { $size: '$challenges' },
          completedChallenges: 0, // À calculer selon l'utilisateur
          averageScore: 0 // À calculer selon l'utilisateur
        }
      }
    ]);

    console.log('Categories found:', categories.length);
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories with challenges:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/challenges/category/:categoryId
router.get('/challenges/category/:categoryId', async (req, res) => {
  try {
    console.log('Fetching challenges for category:', req.params.categoryId);

    const challenges = await Challenge.find({
      categoryId: req.params.categoryId,
      isActive: true
    }).populate('categoryId', 'name description');

    console.log('Challenges found for category:', challenges.length);

    // Ajouter des informations supplémentaires pour chaque challenge
    const enrichedChallenges = challenges.map(challenge => ({
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown'
    }));

    res.json(enrichedChallenges);
  } catch (error) {
    console.error('Error fetching challenges by category:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/challenges - Récupérer tous les challenges disponibles avec leurs catégories
router.get('/challenges', async (req, res) => {
  try {
    console.log('Fetching all available challenges');

    const challenges = await Challenge.find({
      isActive: true
    }).populate('categoryId', 'name description');

    console.log('Total challenges found:', challenges.length);

    // Enrichir les challenges avec des informations supplémentaires
    const enrichedChallenges = challenges.map(challenge => ({
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    }));

    res.json(enrichedChallenges);
  } catch (error) {
    console.error('Error fetching all challenges:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/challenge/:challengeId - Récupérer un challenge spécifique
router.get('/challenge/:challengeId', async (req, res) => {
  try {
    console.log('Fetching challenge:', req.params.challengeId);

    const challenge = await Challenge.findById(req.params.challengeId)
      .populate('categoryId', 'name description');

    if (!challenge) {
      return res.status(404).json({ error: 'Challenge not found' });
    }

    // Enrichir le challenge avec des informations supplémentaires
    const enrichedChallenge = {
      ...challenge.toObject(),
      totalQuestions: challenge.questions ? challenge.questions.length : 0,
      categoryName: challenge.categoryId ? challenge.categoryId.name : 'Unknown',
      category: challenge.categoryId
    };

    res.json(enrichedChallenge);
  } catch (error) {
    console.error('Error fetching challenge by ID:', error);
    res.status(500).json({ error: error.message });
  }
});

// POST /api/quiz/submit
router.post('/submit', async (req, res) => {
  try {
    const { challengeId, userId, answers } = req.body;

    // Récupérer le challenge avec les questions
    const challenge = await Challenge.findById(challengeId);
    if (!challenge) {
      return res.status(404).json({ error: 'Challenge not found' });
    }

    // Calculer le score
    let correctAnswers = 0;
    const correctAnswersDetails = [];
    const incorrectAnswersDetails = [];

    challenge.questions.forEach((question) => {
      const userAnswer = answers[question._id];
      if (userAnswer === question.correct_answer) {
        correctAnswers++;
        correctAnswersDetails.push({
          questionId: question._id,
          question: question.question,
          correctAnswer: question.correct_answer,
          userAnswer: userAnswer
        });
      } else {
        incorrectAnswersDetails.push({
          questionId: question._id,
          question: question.question,
          correctAnswer: question.correct_answer,
          userAnswer: userAnswer
        });
      }
    });

    const percentage = Math.round((correctAnswers / challenge.questions.length) * 100);
    const pointsPerQuestion = challenge.difficulty === 'easy' ? 10 :
                             challenge.difficulty === 'medium' ? 15 : 20;
    const totalScore = correctAnswers * pointsPerQuestion;

    // Sauvegarder le score
    const quizScore = new QuizScore({
      userId: userId,
      challengeId: challengeId,
      categoryId: challenge.categoryId,
      score: totalScore,
      totalQuestions: challenge.questions.length,
      percentage: percentage,
      answers: answers,
      correctAnswers: correctAnswersDetails,
      incorrectAnswers: incorrectAnswersDetails
    });

    await quizScore.save();

    // Créer automatiquement un badge
    let badgeEarned = null;
    let certificateEarned = false;
    try {
      badgeEarned = await Badge.createBadgeFromQuiz({
        userId: userId,
        challengeId: challengeId,
        categoryId: challenge.categoryId,
        percentage: percentage,
        score: totalScore
      });

      // Vérifier si un certificat a été généré
      if (badgeEarned && badgeEarned.certificateImageUrl) {
        certificateEarned = true;
      }
    } catch (badgeError) {
      console.error('Erreur lors de la création du badge:', badgeError);
    }

    // Mettre à jour la progression utilisateur
    const updatedProgress = await updateUserProgress(userId, totalScore);

    res.json({
      score: totalScore,
      totalQuestions: challenge.questions.length,
      percentage,
      correctAnswers: correctAnswersDetails,
      incorrectAnswers: incorrectAnswersDetails,
      badgeEarned: badgeEarned,
      certificateEarned: certificateEarned,
      newTotalScore: updatedProgress.totalScore,
      userProgress: updatedProgress
    });

  } catch (error) {
    console.error('Error submitting quiz:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/progress/:userId
router.get('/progress/:userId', async (req, res) => {
  try {
    let progress = await UserProgress.findOne({ userId: req.params.userId });

    if (!progress) {
      progress = new UserProgress({
        userId: req.params.userId,
        totalScore: 0,
        totalQuizzes: 0,
        averagePercentage: 0,
        categoriesCompleted: [],
        badgesEarned: [],
        certificateEarned: false
      });
      await progress.save();
    }

    res.json(progress);
  } catch (error) {
    console.error('Error fetching user progress:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/leaderboard
router.get('/leaderboard', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    // Utiliser la méthode statique du modèle QuizScore
    const leaderboard = await QuizScore.getLeaderboard(limit);

    // S'assurer que le résultat est un tableau
    const result = Array.isArray(leaderboard) ? leaderboard : [];

    console.log('Leaderboard found:', result.length, 'entries');
    res.json(result);
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    // Retourner un tableau vide en cas d'erreur pour éviter l'erreur findIndex
    res.json([]);
  }
});

// GET /api/quiz/leaderboard/category/:categoryId
router.get('/leaderboard/category/:categoryId', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    const categoryId = req.params.categoryId;

    // Utiliser la méthode statique du modèle QuizScore
    const leaderboard = await QuizScore.getCategoryLeaderboard(categoryId, limit);

    // S'assurer que le résultat est un tableau
    const result = Array.isArray(leaderboard) ? leaderboard : [];

    console.log('Category leaderboard found:', result.length, 'entries');
    res.json(result);
  } catch (error) {
    console.error('Error fetching category leaderboard:', error);
    // Retourner un tableau vide en cas d'erreur pour éviter l'erreur findIndex
    res.json([]);
  }
});

// GET /api/quiz/progress/:userId - Récupérer la progression d'un utilisateur
router.get('/progress/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    console.log('Fetching progress for user:', userId);

    const progress = await UserProgress.findOne({ userId: userId });

    if (!progress) {
      // Créer une progression par défaut si elle n'existe pas
      const newProgress = new UserProgress({
        userId: userId,
        totalScore: 0,
        totalQuizzes: 0,
        averagePercentage: 0,
        categoriesCompleted: [],
        badgesEarned: [],
        certificateEarned: false,
        lastQuizDate: new Date()
      });
      await newProgress.save();
      return res.json(newProgress);
    }

    res.json(progress);
  } catch (error) {
    console.error('Error fetching user progress:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/scores/user/:userId - Récupérer les scores d'un utilisateur
router.get('/scores/user/:userId', async (req, res) => {
  try {
    const userId = req.params.userId;
    console.log('Fetching scores for user:', userId);

    const scores = await QuizScore.find({ userId: userId })
      .populate('challengeId', 'title description difficulty')
      .populate('categoryId', 'name description')
      .sort({ completedAt: -1 })
      .limit(50); // Limiter à 50 derniers scores

    // Calculer les statistiques
    const totalScores = scores.length;
    const totalPoints = scores.reduce((sum, score) => sum + score.score, 0);
    const averageScore = totalScores > 0 ? totalPoints / totalScores : 0;
    const averagePercentage = totalScores > 0 ?
      scores.reduce((sum, score) => sum + score.percentage, 0) / totalScores : 0;

    res.json({
      scores: scores,
      statistics: {
        totalScores,
        totalPoints,
        averageScore: Math.round(averageScore * 100) / 100,
        averagePercentage: Math.round(averagePercentage * 100) / 100
      }
    });
  } catch (error) {
    console.error('Error fetching user scores:', error);
    res.status(500).json({ error: error.message });
  }
});

// Fonction utilitaire pour mettre à jour la progression utilisateur
async function updateUserProgress(userId, newScore) {
  try {
    let progress = await UserProgress.findOne({ userId: userId });

    if (!progress) {
      progress = new UserProgress({
        userId: userId,
        totalScore: newScore,
        totalQuizzes: 1,
        averagePercentage: 0,
        categoriesCompleted: [],
        badgesEarned: [],
        certificateEarned: false,
        lastQuizDate: new Date()
      });
    } else {
      progress.totalScore += newScore;
      progress.totalQuizzes += 1;
      progress.lastQuizDate = new Date();
    }

    // Calculer le pourcentage moyen
    const scores = await QuizScore.find({ userId: userId });
    if (scores.length > 0) {
      const totalPercentage = scores.reduce((sum, score) => sum + score.percentage, 0);
      progress.averagePercentage = Math.round(totalPercentage / scores.length);
    }

    await progress.save();
    return progress;
  } catch (error) {
    console.error('Error updating user progress:', error);
    throw error;
  }
}

module.exports = router;
