/* Styles pour l'affichage des badges */
.badge-container {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation pour les cartes de badge */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.badge-card {
  animation: slideInUp 0.6s ease-out;
  position: relative;
  overflow: hidden;
}

.badge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.badge-card:hover::before {
  left: 100%;
}

/* Styles pour les images de badges */
.badge-image {
  transition: transform 0.3s ease;
}

.badge-card:hover .badge-image {
  transform: scale(1.05);
}

/* Styles pour les certificats */
.certificate-image {
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.certificate-image:hover {
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.02);
}

/* Styles pour les boutons */
.btn-download {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  transition: all 0.3s ease;
}

.btn-download:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-share {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  transition: all 0.3s ease;
}

.btn-share:hover {
  background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Styles pour les badges de type */
.badge-type {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Styles pour les statistiques */
.stats-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stats-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* Animation de chargement */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Styles pour les filtres */
.filter-container {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Styles pour les inputs et selects */
select {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

select:focus {
  border-color: rgba(255, 255, 255, 0.5);
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

select option {
  background: #1f2937;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .md\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Styles pour les messages d'erreur */
.error-message {
  backdrop-filter: blur(10px);
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.5);
  color: rgba(254, 226, 226, 1);
}

/* Animation pour les cartes au hover */
.transform {
  transition: transform 0.3s ease;
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* Styles pour les badges de score */
.score-badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

/* Styles pour les icônes */
.icon-large {
  font-size: 4rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Styles pour les dates */
.date-text {
  font-size: 0.875rem;
  color: rgba(156, 163, 175, 1);
}

/* Styles pour les descriptions */
.description-text {
  line-height: 1.5;
  color: rgba(209, 213, 219, 1);
}
