const express = require('express');
const router = express.Router();
const Feedback = require('../models/Feedback');
const Salon = require('../models/Salon');

// Middleware pour vérifier le rôle utilisateur (à adapter selon votre système d'auth)
const checkUserRole = (req, res, next) => {
  // Supposons que le rôle est passé dans les headers ou dans le token JWT
  const userRole = req.headers['user-role'] || req.user?.role || 'client';
  const userId = req.headers['user-id'] || req.user?.id;

  req.userRole = userRole;
  req.userId = userId;
  next();
};

// GET /api/feedbacks - Récupérer les feedbacks (Admin: tous, Client: seulement les siens)
router.get('/', checkUserRole, async (req, res) => {
  try {
    let feedbacks;

    if (req.userRole === 'admin') {
      // Admin peut voir tous les feedbacks
      feedbacks = await Feedback.find({ isActive: true })
        .populate('userId', 'fullName email')
        .populate('targetUserId', 'fullName email')
        .populate('salonId', 'nom description')
        .sort({ createdAt: -1 });
    } else {
      // Client ne peut voir que ses feedbacks (donnés ou reçus)
      feedbacks = await Feedback.getUserFeedbacks(req.userId);
    }

    res.json(feedbacks);
  } catch (error) {
    console.error('Erreur lors de la récupération des feedbacks:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/feedbacks/:id - Récupérer un feedback spécifique
router.get('/:id', checkUserRole, async (req, res) => {
  try {
    const feedback = await Feedback.findById(req.params.id)
      .populate('userId', 'fullName email')
      .populate('targetUserId', 'fullName email')
      .populate('salonId', 'nom description');

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback non trouvé' });
    }

    // Vérifier les permissions
    if (req.userRole !== 'admin' &&
        feedback.userId.toString() !== req.userId &&
        feedback.targetUserId.toString() !== req.userId) {
      return res.status(403).json({ error: 'Accès non autorisé' });
    }

    res.json(feedback);
  } catch (error) {
    console.error('Erreur lors de la récupération du feedback:', error);
    res.status(500).json({ error: error.message });
  }
});

// POST /api/feedbacks - Créer un nouveau feedback (Client seulement)
router.post('/', checkUserRole, async (req, res) => {
  try {
    if (req.userRole === 'admin') {
      return res.status(403).json({ error: 'Les admins ne peuvent pas créer de feedbacks' });
    }

    const { targetUserId, salonId, rating, comment, sessionStartTime } = req.body;

    // Vérifier si le feedback peut être créé
    const canCreate = await Feedback.canCreateFeedback(
      req.userId,
      targetUserId,
      salonId,
      new Date(sessionStartTime)
    );

    if (!canCreate.canCreate) {
      return res.status(400).json({ error: canCreate.reason });
    }

    // Vérifier que l'utilisateur fait partie du salon
    const salon = await Salon.findById(salonId);
    if (!salon) {
      return res.status(404).json({ error: 'Salon non trouvé' });
    }

    const isParticipant = salon.participants.some(p =>
      p.userId.toString() === req.userId && p.isActive
    ) || salon.createurId.toString() === req.userId;

    if (!isParticipant) {
      return res.status(403).json({ error: 'Vous devez être participant du salon pour donner un feedback' });
    }

    const feedback = new Feedback({
      userId: req.userId,
      targetUserId,
      salonId,
      rating,
      comment,
      sessionStartTime: new Date(sessionStartTime)
    });

    await feedback.save();

    // Populate les références pour la réponse
    await feedback.populate(['userId', 'targetUserId', 'salonId']);

    res.status(201).json(feedback);
  } catch (error) {
    console.error('Erreur lors de la création du feedback:', error);
    res.status(500).json({ error: error.message });
  }
});

// PUT /api/feedbacks/:id - Modifier un feedback (Client seulement, ses propres feedbacks)
router.put('/:id', checkUserRole, async (req, res) => {
  try {
    if (req.userRole === 'admin') {
      return res.status(403).json({ error: 'Les admins ne peuvent pas modifier de feedbacks' });
    }

    const feedback = await Feedback.findById(req.params.id);

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback non trouvé' });
    }

    // Vérifier que l'utilisateur est le propriétaire du feedback
    if (feedback.userId.toString() !== req.userId) {
      return res.status(403).json({ error: 'Vous ne pouvez modifier que vos propres feedbacks' });
    }

    const { rating, comment } = req.body;

    if (rating !== undefined) feedback.rating = rating;
    if (comment !== undefined) feedback.comment = comment;

    await feedback.save();
    await feedback.populate(['userId', 'targetUserId', 'salonId']);

    res.json(feedback);
  } catch (error) {
    console.error('Erreur lors de la modification du feedback:', error);
    res.status(500).json({ error: error.message });
  }
});

// DELETE /api/feedbacks/:id - Supprimer un feedback (Client seulement, ses propres feedbacks)
router.delete('/:id', checkUserRole, async (req, res) => {
  try {
    if (req.userRole === 'admin') {
      return res.status(403).json({ error: 'Les admins ne peuvent pas supprimer de feedbacks' });
    }

    const feedback = await Feedback.findById(req.params.id);

    if (!feedback) {
      return res.status(404).json({ error: 'Feedback non trouvé' });
    }

    // Vérifier que l'utilisateur est le propriétaire du feedback
    if (feedback.userId.toString() !== req.userId) {
      return res.status(403).json({ error: 'Vous ne pouvez supprimer que vos propres feedbacks' });
    }

    // Soft delete
    feedback.isActive = false;
    await feedback.save();

    res.json({ message: 'Feedback supprimé avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression du feedback:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/feedbacks/salon/:salonId - Récupérer les feedbacks d'un salon
router.get('/salon/:salonId', checkUserRole, async (req, res) => {
  try {
    const feedbacks = await Feedback.getFeedbacksForSalon(req.params.salonId);

    // Si ce n'est pas un admin, filtrer pour ne montrer que les feedbacks pertinents
    if (req.userRole !== 'admin') {
      const filteredFeedbacks = feedbacks.filter(feedback =>
        feedback.userId._id.toString() === req.userId ||
        feedback.targetUserId._id.toString() === req.userId
      );
      return res.json(filteredFeedbacks);
    }

    res.json(feedbacks);
  } catch (error) {
    console.error('Erreur lors de la récupération des feedbacks du salon:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/feedbacks/user/:userId/average - Récupérer la note moyenne d'un utilisateur
router.get('/user/:userId/average', async (req, res) => {
  try {
    const average = await Feedback.getAverageRatingForUser(req.params.userId);
    res.json(average);
  } catch (error) {
    console.error('Erreur lors du calcul de la moyenne:', error);
    res.status(500).json({ error: error.message });
  }
});

// GET /api/feedbacks/top-rated - Récupérer les utilisateurs les mieux notés
router.get('/top-rated', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;

    const topUsers = await Feedback.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$targetUserId',
          averageRating: { $avg: '$rating' },
          count: { $sum: 1 }
        }
      },
      { $match: { count: { $gte: 1 } } }, // Au moins 1 feedback
      { $sort: { averageRating: -1 } },
      { $limit: limit },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          _id: 1,
          averageRating: { $round: ['$averageRating', 2] },
          count: 1,
          'user.fullName': 1,
          'user.email': 1
        }
      }
    ]);

    res.json(topUsers);
  } catch (error) {
    console.error('Erreur lors de la récupération du top des utilisateurs:', error);
    res.status(500).json({ error: error.message });
  }
});

// POST /api/feedbacks/trigger/:salonId - Déclencher les feedbacks pour une session (automatique)
router.post('/trigger/:salonId', async (req, res) => {
  try {
    const salon = await Salon.findById(req.params.salonId)
      .populate('participants.userId', 'fullName email');

    if (!salon) {
      return res.status(404).json({ error: 'Salon non trouvé' });
    }

    const feedbackPrompts = await salon.checkAndTriggerFeedbacks();

    if (feedbackPrompts) {
      res.json({
        message: 'Feedbacks déclenchés',
        prompts: feedbackPrompts
      });
    } else {
      res.json({
        message: 'Aucun feedback à déclencher pour le moment'
      });
    }
  } catch (error) {
    console.error('Erreur lors du déclenchement des feedbacks:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
