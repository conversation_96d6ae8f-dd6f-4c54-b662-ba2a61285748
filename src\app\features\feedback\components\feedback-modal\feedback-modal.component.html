<!-- Modal de feedback -->
<div class="modal-overlay" *ngIf="isVisible" (click)="onBackdropClick($event)">
  <div class="modal-content">
    <div class="modal-header">
      <h3>💬 Donner un feedback</h3>
      <button class="close-button" (click)="onClose()" type="button">
        <span>&times;</span>
      </button>
    </div>

    <div class="modal-body">
      <div class="user-info" *ngIf="targetUser">
        <div class="user-avatar">
          <img [src]="targetUser.profilePicture || 'assets/default-avatar.svg'"
               [alt]="targetUser.fullName">
        </div>
        <div class="user-details">
          <h4>{{ targetUser.fullName }}</h4>
          <p>Comment s'est passée votre interaction ?</p>
        </div>
      </div>

      <form [formGroup]="feedbackForm" (ngSubmit)="onSubmit()">
        <!-- Rating avec étoiles -->
        <div class="form-group">
          <label for="rating">Votre évaluation :</label>
          <app-star-rating
            formControlName="rating"
            [rating]="feedbackForm.get('rating')?.value || 0"
            size="large">
          </app-star-rating>
          <div class="rating-description" *ngIf="feedbackForm.get('rating')?.value > 0">
            {{ getRatingText(feedbackForm.get('rating')?.value) }}
          </div>
          <div class="error-message" *ngIf="rating?.invalid && rating?.touched">
            Veuillez donner une note entre 1 et 5 étoiles
          </div>
        </div>

        <!-- Commentaire -->
        <div class="form-group">
          <label for="comment">Commentaire (optionnel) :</label>
          <textarea
            id="comment"
            formControlName="comment"
            placeholder="Partagez votre expérience..."
            rows="4"
            maxlength="500">
          </textarea>
          <div class="character-count">
            {{ comment?.value?.length || 0 }}/500 caractères
          </div>
          <div class="error-message" *ngIf="comment?.invalid && comment?.touched">
            Le commentaire ne peut pas dépasser 500 caractères
          </div>
        </div>

        <!-- Messages -->
        <div class="success-message" *ngIf="successMessage">
          <span class="icon">✅</span>
          {{ successMessage }}
        </div>

        <div class="error-message" *ngIf="errorMessage">
          <span class="icon">❌</span>
          {{ errorMessage }}
        </div>

        <!-- Actions -->
        <div class="modal-actions">
          <button type="button"
                  class="btn btn-secondary"
                  (click)="onClose()"
                  [disabled]="isSubmitting">
            Annuler
          </button>
          <button type="submit"
                  class="btn btn-primary"
                  [disabled]="feedbackForm.invalid || isSubmitting">
            <span *ngIf="isSubmitting" class="spinner"></span>
            {{ isSubmitting ? 'Envoi...' : 'Envoyer le feedback' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
