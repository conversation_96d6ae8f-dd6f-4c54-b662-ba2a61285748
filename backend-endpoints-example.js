// EXEMPLE D'ENDPOINTS BACKEND REQUIS POUR LE SYSTÈME DE QUIZ
// À ajouter dans votre backend Node.js/Express

// ===== ROUTES QUIZ =====

// GET /api/quiz/categories-with-challenges
app.get('/api/quiz/categories-with-challenges', async (req, res) => {
  try {
    const categories = await Category.aggregate([
      {
        $lookup: {
          from: 'challenges',
          localField: '_id',
          foreignField: 'categoryId',
          as: 'challenges'
        }
      },
      {
        $addFields: {
          totalChallenges: { $size: '$challenges' },
          completedChallenges: 0, // À calculer selon l'utilisateur
          averageScore: 0 // À calculer selon l'utilisateur
        }
      }
    ]);
    res.json(categories);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/challenges/category/:categoryId
app.get('/api/quiz/challenges/category/:categoryId', async (req, res) => {
  try {
    const challenges = await Challenge.find({ 
      categoryId: req.params.categoryId,
      isActive: true 
    }).populate('categoryId');
    res.json(challenges);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// POST /api/quiz/submit
app.post('/api/quiz/submit', async (req, res) => {
  try {
    const { challengeId, userId, answers } = req.body;
    
    // Récupérer le challenge avec les questions
    const challenge = await Challenge.findById(challengeId);
    
    // Calculer le score
    let correctAnswers = 0;
    challenge.questions.forEach((question, index) => {
      if (answers[question._id] === question.correct_answer) {
        correctAnswers++;
      }
    });
    
    const percentage = Math.round((correctAnswers / challenge.questions.length) * 100);
    const pointsPerQuestion = challenge.difficulty === 'easy' ? 10 : 
                             challenge.difficulty === 'medium' ? 15 : 20;
    const totalScore = correctAnswers * pointsPerQuestion;
    
    // Sauvegarder le score
    const quizScore = new QuizScore({
      userId,
      challengeId,
      categoryId: challenge.categoryId,
      score: totalScore,
      totalQuestions: challenge.questions.length,
      percentage,
      completedAt: new Date()
    });
    
    await quizScore.save();
    
    // Mettre à jour la progression utilisateur
    await updateUserProgress(userId, totalScore);
    
    // Attribuer un badge
    const badge = await assignBadgeByScore(userId, percentage, challengeId, challenge.categoryId);
    
    // Vérifier l'éligibilité au certificat
    const certificateCheck = await checkCertificateEligibility(userId);
    
    res.json({
      score: totalScore,
      totalQuestions: challenge.questions.length,
      percentage,
      correctAnswers: challenge.questions.filter((q, i) => 
        answers[q._id] === q.correct_answer
      ),
      incorrectAnswers: challenge.questions.filter((q, i) => 
        answers[q._id] !== q.correct_answer
      ),
      badgeEarned: badge,
      certificateEarned: certificateCheck.eligible,
      newTotalScore: certificateCheck.totalScore
    });
    
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/progress/:userId
app.get('/api/quiz/progress/:userId', async (req, res) => {
  try {
    let progress = await UserProgress.findOne({ userId: req.params.userId });
    
    if (!progress) {
      progress = new UserProgress({
        userId: req.params.userId,
        totalScore: 0,
        totalQuizzes: 0,
        averagePercentage: 0,
        categoriesCompleted: [],
        badgesEarned: [],
        certificateEarned: false
      });
      await progress.save();
    }
    
    res.json(progress);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// GET /api/quiz/leaderboard
app.get('/api/quiz/leaderboard', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    
    const leaderboard = await UserProgress.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $lookup: {
          from: 'badges',
          localField: 'userId',
          foreignField: 'userId',
          as: 'badges'
        }
      },
      {
        $addFields: {
          userName: '$user.fullName',
          highestBadge: {
            $arrayElemAt: [
              {
                $map: {
                  input: { $slice: [{ $sortArray: { input: '$badges', sortBy: { awardedAt: -1 } } }, 1] },
                  as: 'badge',
                  in: '$$badge.type'
                }
              },
              0
            ]
          }
        }
      },
      {
        $sort: { totalScore: -1 }
      },
      {
        $limit: limit
      },
      {
        $project: {
          userId: 1,
          userName: 1,
          totalScore: 1,
          totalQuizzes: 1,
          averageScore: '$averagePercentage',
          highestBadge: 1
        }
      }
    ]);
    
    res.json(leaderboard);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ===== FONCTIONS UTILITAIRES =====

async function updateUserProgress(userId, newScore) {
  const progress = await UserProgress.findOne({ userId });
  
  if (progress) {
    progress.totalScore += newScore;
    progress.totalQuizzes += 1;
    
    // Recalculer la moyenne
    const allScores = await QuizScore.find({ userId });
    const totalPercentage = allScores.reduce((sum, score) => sum + score.percentage, 0);
    progress.averagePercentage = Math.round(totalPercentage / allScores.length);
    
    progress.lastQuizDate = new Date();
    await progress.save();
  }
  
  return progress;
}

async function assignBadgeByScore(userId, percentage, challengeId, categoryId) {
  let badgeType;
  if (percentage >= 90) badgeType = 'EXPERT';
  else if (percentage >= 70) badgeType = 'INTERMEDIATE';
  else if (percentage >= 50) badgeType = 'BEGINNER';
  else badgeType = 'PARTICIPANT';
  
  const badge = new Badge({
    userId,
    challengeId,
    name: `${badgeType} Badge`,
    type: badgeType,
    percentage,
    awardedAt: new Date()
  });
  
  await badge.save();
  return badge;
}

async function checkCertificateEligibility(userId) {
  const progress = await UserProgress.findOne({ userId });
  
  if (progress && progress.totalScore >= 1000 && !progress.certificateEarned) {
    progress.certificateEarned = true;
    progress.certificateEarnedAt = new Date();
    await progress.save();
    
    return { eligible: true, totalScore: progress.totalScore };
  }
  
  return { 
    eligible: false, 
    totalScore: progress ? progress.totalScore : 0 
  };
}

// ===== MODÈLES MONGOOSE =====

const QuizScoreSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  challengeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Challenge', required: true },
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
  score: { type: Number, required: true },
  totalQuestions: { type: Number, required: true },
  percentage: { type: Number, required: true },
  timeSpent: { type: Number },
  completedAt: { type: Date, default: Date.now }
});

const UserProgressSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
  totalScore: { type: Number, default: 0 },
  totalQuizzes: { type: Number, default: 0 },
  averagePercentage: { type: Number, default: 0 },
  categoriesCompleted: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Category' }],
  badgesEarned: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Badge' }],
  certificateEarned: { type: Boolean, default: false },
  certificateEarnedAt: { type: Date },
  lastQuizDate: { type: Date }
});

const QuizScore = mongoose.model('QuizScore', QuizScoreSchema);
const UserProgress = mongoose.model('UserProgress', UserProgressSchema);
