.skills-matching-container {
  padding: 20px;
  font-family: Arial, sans-serif;
}

h2 {
  color: #333;
  margin-bottom: 20px;
}

h3 {
  color: #555;
  margin: 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.loading-spinner {
  text-align: center;
  padding: 30px;
}

.error-message {
  background-color: #ffebee;
  padding: 15px;
  border-radius: 4px;
  margin: 20px 0;
  text-align: center;
}

.error-message button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.no-data {
  text-align: center;
  color: #757575;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.users-grid, .skills-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.user-card, .skill-card {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-card h4, .skill-card h4 {
  margin-top: 0;
  color: #333;
}

.common-skills {
  margin: 10px 0;
}

.common-skills h5 {
  margin-bottom: 5px;
  color: #555;
}

.common-skills ul {
  padding-left: 20px;
  margin: 0;
}

.card-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.card-actions button {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.card-actions button:hover {
  background-color: #388e3c;
}

.card-actions button:last-child {
  background-color: #2196f3;
}

.card-actions button:last-child:hover {
  background-color: #1976d2;
}