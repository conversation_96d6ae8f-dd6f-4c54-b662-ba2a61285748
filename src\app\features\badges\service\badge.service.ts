import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Badge } from '../models/badge.model';

@Injectable({
  providedIn: 'root'
})
export class BadgeService {

  private apiUrl = 'http://localhost:3000/api/badges';

  constructor(private http: HttpClient) {}

  getAllBadges(): Observable<Badge[]> {
    return this.http.get<Badge[]>(`${this.apiUrl}`);
  }

  getBadgeById(_id: any): Observable<Badge> {
    return this.http.get<Badge>(`${this.apiUrl}/${_id}`);
  }

  getBadgesByUser(userId: any): Observable<Badge[]> {
    console.log(`Appel à l'API pour récupérer les badges de l'utilisateur avec ID : ${userId}`);
    return this.http.get<Badge[]>(`${this.apiUrl}/user/${userId}`);
  }


  createBadge(badge: Partial<Badge>): Observable<Badge> {
    return this.http.post<Badge>(`${this.apiUrl}`, badge);
  }

  assignBadge(userId: string, score: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/assign`, { userId, score });
  }

  /*updateBadge(_id: any, badge: Partial<Badge>): Observable<Badge> {
    return this.http.put<Badge>(`${this.apiUrl}/${_id}`, badge);
  }*/

  deleteBadge(_id: string): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${_id}`);
  }

  getLeaderboard(): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/leaderboard`);
  }

  // Nouvelles méthodes pour le système de quiz et certification
  assignBadgeByScore(userId: string, score: number, challengeId: string, categoryId: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/assign-by-score`, {
      userId,
      score,
      challengeId,
      categoryId
    });
  }

  getUserTotalScore(userId: string): Observable<{ totalScore: number; totalQuizzes: number }> {
    return this.http.get<{ totalScore: number; totalQuizzes: number }>(`${this.apiUrl}/user/${userId}/total-score`);
  }

  checkCertificateEligibility(userId: string): Observable<{ eligible: boolean; totalScore: number; progress: number }> {
    return this.http.get<{ eligible: boolean; totalScore: number; progress: number }>(`${this.apiUrl}/user/${userId}/certificate-eligibility`);
  }

  awardCertificate(userId: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/award-certificate`, { userId });
  }

  getUserCertificates(userId: string): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/user/${userId}/certificates`);
  }

  getBadgesByCategory(userId: string, categoryId: string): Observable<Badge[]> {
    return this.http.get<Badge[]>(`${this.apiUrl}/user/${userId}/category/${categoryId}`);
  }

  getLeaderboardByCategory(categoryId: string, limit: number = 10): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/leaderboard/category/${categoryId}?limit=${limit}`);
  }

  getUserProgress(userId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/user/${userId}/progress`);
  }

  updateUserProgress(userId: string, progressData: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/user/${userId}/progress`, progressData);
  }

  getBadgeStatistics(): Observable<any> {
    return this.http.get(`${this.apiUrl}/statistics`);
  }

  getCertificateTemplate(userId: string): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/user/${userId}/certificate-template`, {
      responseType: 'blob'
    });
  }

  generateCertificatePDF(userId: string, certificateData: any): Observable<Blob> {
    return this.http.post(`${this.apiUrl}/generate-certificate-pdf`,
      { userId, ...certificateData },
      { responseType: 'blob' }
    );
  }
}
