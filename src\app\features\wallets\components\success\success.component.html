<div class="max-w-md mx-auto p-6">
  <div class="bg-white rounded-xl shadow-lg p-8 text-center">
    <div class="mb-4 flex justify-center">
      <div class="rounded-full bg-green-100 p-3">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
      </div>
    </div>
    
    <h2 class="text-2xl font-bold text-gray-800 mb-2">Payment Successful!</h2>
    
    <div *ngIf="loading$ | async" class="flex justify-center my-4">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
    </div>
    
    <p class="text-gray-600 mb-6">Your wallet has been topped up successfully.</p>
    
    <div *ngIf="error$ | async as error" class="text-red-500 text-sm mb-4">
      {{ error }}
    </div>
    
    <div class="flex flex-col space-y-3">
      <a routerLink="/wallets" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition">
        View My Wallet
      </a>
      <a routerLink="/wallets/top-up" class="text-purple-600 hover:text-purple-800">
        Top Up Again
      </a>
    </div>
  </div>
</div>
