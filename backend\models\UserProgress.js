const mongoose = require('mongoose');

const UserProgressSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  totalScore: {
    type: Number,
    default: 0,
    min: 0
  },
  totalQuizzes: {
    type: Number,
    default: 0,
    min: 0
  },
  averagePercentage: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  categoriesCompleted: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }],
  badgesEarned: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Badge'
  }],
  certificateEarned: {
    type: Boolean,
    default: false
  },
  certificateEarnedAt: {
    type: Date
  },
  lastQuizDate: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index pour optimiser les requêtes
UserProgressSchema.index({ userId: 1 });
UserProgressSchema.index({ totalScore: -1 });
UserProgressSchema.index({ certificateEarned: 1 });

// Middleware pour mettre à jour updatedAt
UserProgressSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Méthodes statiques
UserProgressSchema.statics.getUserRanking = async function(userId) {
  const userProgress = await this.findOne({ userId });
  if (!userProgress) return { rank: -1, totalUsers: 0 };
  
  const higherScoreCount = await this.countDocuments({
    totalScore: { $gt: userProgress.totalScore }
  });
  
  const totalUsers = await this.countDocuments({});
  
  return {
    rank: higherScoreCount + 1,
    totalUsers: totalUsers,
    userScore: userProgress.totalScore
  };
};

UserProgressSchema.statics.getTopUsers = async function(limit = 10) {
  return await this.find({})
    .populate('userId', 'fullName email')
    .sort({ totalScore: -1 })
    .limit(limit)
    .select('userId totalScore totalQuizzes averagePercentage certificateEarned lastQuizDate');
};

UserProgressSchema.statics.getCertifiedUsers = async function() {
  return await this.find({ certificateEarned: true })
    .populate('userId', 'fullName email')
    .sort({ certificateEarnedAt: -1 })
    .select('userId totalScore certificateEarnedAt');
};

module.exports = mongoose.model('UserProgress', UserProgressSchema);
