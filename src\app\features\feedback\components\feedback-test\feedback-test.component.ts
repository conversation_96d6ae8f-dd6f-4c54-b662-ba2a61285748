import { Component } from '@angular/core';

@Component({
  selector: 'app-feedback-test',
  template: `
    <div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-4xl font-bold text-white text-center mb-8">
          🧪 Test des Composants Feedback
        </h1>
        
        <!-- Test Star Rating -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 mb-8">
          <h2 class="text-2xl font-bold text-white mb-4">⭐ Test Star Rating</h2>
          <div class="space-y-4">
            <div>
              <label class="block text-white mb-2">Rating interactif:</label>
              <app-star-rating 
                [(ngModel)]="rating" 
                [readonly]="false"
                size="large">
              </app-star-rating>
              <p class="text-white mt-2">Valeur: {{ rating }}/5</p>
            </div>
            
            <div>
              <label class="block text-white mb-2">Rating en lecture seule:</label>
              <app-star-rating 
                [rating]="4" 
                [readonly]="true"
                size="medium">
              </app-star-rating>
            </div>
            
            <div>
              <label class="block text-white mb-2">Rating petit:</label>
              <app-star-rating 
                [rating]="3" 
                [readonly]="true"
                size="small">
              </app-star-rating>
            </div>
          </div>
        </div>
        
        <!-- Test Feedback Modal -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 mb-8">
          <h2 class="text-2xl font-bold text-white mb-4">💬 Test Feedback Modal</h2>
          <div class="space-y-4">
            <button 
              (click)="showModal = true"
              class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              Ouvrir le Modal de Feedback
            </button>
            
            <div *ngIf="lastFeedback" class="bg-green-500/20 border border-green-500 text-green-100 p-4 rounded-lg">
              <h3 class="font-bold">Dernier feedback envoyé:</h3>
              <p>Note: {{ lastFeedback.rating }}/5</p>
              <p>Commentaire: {{ lastFeedback.comment || 'Aucun commentaire' }}</p>
            </div>
          </div>
        </div>
        
        <!-- Informations de test -->
        <div class="bg-white/10 backdrop-blur-md rounded-xl p-6">
          <h2 class="text-2xl font-bold text-white mb-4">ℹ️ Informations de Test</h2>
          <div class="text-white space-y-2">
            <p><strong>Routes disponibles:</strong></p>
            <ul class="list-disc list-inside ml-4 space-y-1">
              <li><code>/feedback/modal-test</code> - Test du modal de feedback</li>
              <li><code>/feedback/star-test</code> - Test du composant étoiles</li>
              <li><code>/feedback/average-rating</code> - Test de la note moyenne</li>
              <li><code>/feedback/top-rated-users</code> - Test des utilisateurs les mieux notés</li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- Modal de feedback -->
      <app-feedback-modal
        [isVisible]="showModal"
        [targetUser]="mockUser"
        [salonId]="mockSalonId"
        [sessionStartTime]="mockSessionStart"
        (close)="showModal = false"
        (feedbackSubmitted)="onFeedbackSubmitted($event)">
      </app-feedback-modal>
    </div>
  `,
  styles: [`
    code {
      background: rgba(255, 255, 255, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
    }
  `]
})
export class FeedbackTestComponent {
  rating: number = 0;
  showModal: boolean = false;
  lastFeedback: any = null;
  
  // Données de test
  mockUser = {
    _id: 'test-user-id',
    fullName: 'Utilisateur Test',
    email: '<EMAIL>',
    profilePicture: null
  };
  
  mockSalonId = 'test-salon-id';
  mockSessionStart = new Date(Date.now() - 6 * 60 * 1000); // Il y a 6 minutes
  
  onFeedbackSubmitted(feedback: any): void {
    this.lastFeedback = feedback;
    console.log('Feedback reçu:', feedback);
  }
}
