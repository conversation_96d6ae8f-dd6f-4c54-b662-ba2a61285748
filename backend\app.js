const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Connexion MongoDB
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/skillshub';
mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('✅ Connecté à MongoDB'))
.catch(err => console.error('❌ Erreur de connexion MongoDB:', err));

// Routes existantes (ajoutez vos routes existantes ici)
// Exemple de routes que vous pourriez avoir :

// Routes d'authentification
// app.use('/api/auth', require('./routes/auth'));

// Routes des catégories
// app.use('/api/Category', require('./routes/category'));

// Routes des challenges
// app.use('/api/challenges', require('./routes/challenges'));

// Routes des badges
// app.use('/api/badges', require('./routes/badges'));

// Routes des sessions
// app.use('/api/sessions', require('./routes/sessions'));

// Routes des salons
// app.use('/api/salons', require('./routes/salons'));

// Routes des wallets
// app.use('/api/wallets', require('./routes/wallets'));

// Routes des feedbacks
// app.use('/api/feedbacks', require('./routes/feedbacks'));

// Routes des forums
// app.use('/api/forums', require('./routes/forums'));

// Routes des skills/marketplace
// app.use('/api/skill-market', require('./routes/skill-market'));

// Routes des utilisateurs
// app.use('/api/user', require('./routes/user'));

// ===== NOUVELLES ROUTES =====
// Routes quiz
app.use('/api/quiz', require('./routes/quiz'));

// Routes badges
app.use('/api/badges', require('./routes/badges'));

// Routes feedbacks
app.use('/api/feedbacks', require('./routes/feedback'));

// Route de test
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Serveur backend fonctionnel',
    timestamp: new Date().toISOString(),
    routes: {
      quiz: '/api/quiz/categories-with-challenges',
      leaderboard: '/api/quiz/leaderboard'
    }
  });
});

// Route par défaut
app.get('/', (req, res) => {
  res.json({
    message: 'API SkillsHub Backend',
    version: '1.0.0',
    endpoints: [
      '/api/quiz/categories-with-challenges',
      '/api/quiz/leaderboard',
      '/api/quiz/submit',
      '/api/test'
    ]
  });
});

// Gestion des erreurs 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route non trouvée',
    path: req.originalUrl,
    method: req.method,
    availableRoutes: [
      'GET /api/quiz/categories-with-challenges',
      'GET /api/quiz/leaderboard',
      'POST /api/quiz/submit',
      'GET /api/test'
    ]
  });
});

// Gestion des erreurs globales
app.use((err, req, res, next) => {
  console.error('Erreur serveur:', err);
  res.status(500).json({
    error: 'Erreur interne du serveur',
    message: err.message
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur démarré sur le port ${PORT}`);
  console.log(`📍 URL: http://localhost:${PORT}`);
  console.log(`🔗 Test quiz: http://localhost:${PORT}/api/quiz/categories-with-challenges`);
  console.log(`🏆 Test leaderboard: http://localhost:${PORT}/api/quiz/leaderboard`);
});

module.exports = app;
