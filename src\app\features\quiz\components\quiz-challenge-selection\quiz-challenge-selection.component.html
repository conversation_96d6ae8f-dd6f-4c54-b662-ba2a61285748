<div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">
        🎯 Choisissez votre Challenge
      </h1>
      <p class="text-xl text-gray-300 mb-6">
        Sélectionnez un challenge et commencez à tester vos compétences !
      </p>

      <!-- Navigation rapide -->
      <div class="flex justify-center space-x-4 mb-6">
        <button
          (click)="goToCategories()"
          class="px-4 py-2 bg-white/10 backdrop-blur-md text-white rounded-lg hover:bg-white/20 transition-colors">
          📂 Vue par catégories
        </button>
        <button
          (click)="goToProfile()"
          class="px-4 py-2 bg-white/10 backdrop-blur-md text-white rounded-lg hover:bg-white/20 transition-colors">
          👤 Mon profil
        </button>
        <button
          (click)="goToLeaderboard()"
          class="px-4 py-2 bg-white/10 backdrop-blur-md text-white rounded-lg hover:bg-white/20 transition-colors">
          🏆 Classement
        </button>
      </div>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded-lg mb-6">
      {{ errorMessage }}
      <button (click)="errorMessage = null" class="float-right text-red-100 hover:text-white">×</button>
    </div>

    <!-- Filtres -->
    <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 mb-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        <!-- Recherche -->
        <div>
          <label class="block text-white font-medium mb-2">Rechercher:</label>
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onFilterChange()"
            placeholder="Titre, description, catégorie..."
            class="w-full px-3 py-2 bg-white/20 text-white placeholder-gray-300 rounded-lg border border-white/30 focus:border-white/50 focus:outline-none">
        </div>

        <!-- Filtre par catégorie -->
        <div>
          <label class="block text-white font-medium mb-2">Catégorie:</label>
          <select
            [(ngModel)]="selectedCategory"
            (change)="onFilterChange()"
            class="w-full px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:border-white/50 focus:outline-none">
            <option value="all">Toutes les catégories</option>
            <option *ngFor="let category of categories" [value]="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- Filtre par difficulté -->
        <div>
          <label class="block text-white font-medium mb-2">Difficulté:</label>
          <select
            [(ngModel)]="selectedDifficulty"
            (change)="onFilterChange()"
            class="w-full px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:border-white/50 focus:outline-none">
            <option value="all">Toutes</option>
            <option value="easy">Facile</option>
            <option value="medium">Moyen</option>
            <option value="hard">Difficile</option>
          </select>
        </div>

        <!-- Actions -->
        <div class="flex space-x-2">
          <button
            (click)="clearFilters()"
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            Effacer
          </button>
          <button
            (click)="refreshChallenges()"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            🔄
          </button>
        </div>
      </div>
    </div>

    <!-- Statistiques -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-2xl font-bold text-white">{{ challenges.length }}</div>
        <div class="text-gray-300">Challenges totaux</div>
      </div>
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-2xl font-bold text-blue-400">{{ filteredChallenges.length }}</div>
        <div class="text-gray-300">Challenges filtrés</div>
      </div>
      <div class="bg-white/10 backdrop-blur-md rounded-xl p-4 text-center">
        <div class="text-2xl font-bold text-green-400">{{ categories.length }}</div>
        <div class="text-gray-300">Catégories</div>
      </div>
    </div>

    <!-- Loading -->
    <div *ngIf="loading" class="text-center text-white">
      <div class="text-6xl mb-4">⏳</div>
      <h3 class="text-2xl font-bold mb-4">Chargement des challenges...</h3>
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
    </div>

    <!-- Liste des challenges -->
    <div *ngIf="!loading && filteredChallenges.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        *ngFor="let challenge of filteredChallenges"
        class="bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition-all duration-300 transform hover:scale-105">

        <!-- Header du challenge -->
        <div class="flex items-start justify-between mb-4">
          <div class="text-4xl">{{ getChallengeIcon(challenge.title) }}</div>
          <span
            [class]="getDifficultyBadgeClass(challenge.difficulty)"
            class="px-2 py-1 rounded-full text-xs font-semibold">
            {{ getDifficultyIcon(challenge.difficulty) }} {{ challenge.difficulty.charAt(0).toUpperCase() + challenge.difficulty.slice(1) }}
          </span>
        </div>

        <!-- Titre et description -->
        <div class="mb-4">
          <h3 class="text-xl font-bold text-white mb-2">{{ challenge.title }}</h3>
          <p class="text-gray-300 text-sm line-clamp-3 mb-2">{{ challenge.description }}</p>

          <!-- Catégorie -->
          <div class="flex items-center text-sm text-blue-300 mb-2">
            <span class="mr-1">📂</span>
            {{ challenge.categoryName || challenge.category?.name || 'Catégorie inconnue' }}
          </div>
        </div>

        <!-- Statistiques -->
        <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
          <div class="bg-white/10 rounded-lg p-3 text-center">
            <div class="text-lg font-bold text-white">{{ challenge.totalQuestions || challenge.questions?.length || 0 }}</div>
            <div class="text-gray-300">Questions</div>
          </div>
          <div class="bg-white/10 rounded-lg p-3 text-center">
            <div class="text-lg font-bold text-white">{{ getEstimatedTime(challenge.totalQuestions || challenge.questions?.length || 0) }}</div>
            <div class="text-gray-300">Durée estimée</div>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="flex space-x-2">
          <button
            (click)="startQuiz(challenge)"
            [disabled]="!challenge.isActive || (challenge.totalQuestions || challenge.questions?.length || 0) === 0"
            class="flex-1 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="challenge.isActive && (challenge.totalQuestions || challenge.questions?.length || 0) > 0">🚀 Challenge</span>
            <span *ngIf="!challenge.isActive">❌ Inactif</span>
            <span *ngIf="challenge.isActive && (challenge.totalQuestions || challenge.questions?.length || 0) === 0">❌ Vide</span>
          </button>

          <button
            (click)="playQuiz(challenge)"
            [disabled]="!challenge.isActive || (challenge.totalQuestions || challenge.questions?.length || 0) === 0"
            class="flex-1 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-lg font-semibold hover:from-green-700 hover:to-teal-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
            <span *ngIf="challenge.isActive && (challenge.totalQuestions || challenge.questions?.length || 0) > 0">▶️ Jouer</span>
            <span *ngIf="!challenge.isActive">❌ Inactif</span>
            <span *ngIf="challenge.isActive && (challenge.totalQuestions || challenge.questions?.length || 0) === 0">❌ Vide</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Message si aucun challenge -->
    <div *ngIf="!loading && filteredChallenges.length === 0" class="text-center text-white">
      <div class="text-6xl mb-4">🎯</div>
      <h3 class="text-2xl font-bold mb-4">Aucun challenge trouvé</h3>
      <p class="text-gray-300 mb-6">
        <span *ngIf="challenges.length === 0">Aucun challenge disponible pour le moment.</span>
        <span *ngIf="challenges.length > 0">Aucun challenge ne correspond à vos critères de recherche.</span>
      </p>
      <div class="space-x-4">
        <button
          (click)="clearFilters()"
          *ngIf="challenges.length > 0"
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Effacer les filtres
        </button>
        <button
          (click)="refreshChallenges()"
          class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          Actualiser
        </button>
      </div>
    </div>
  </div>
</div>
