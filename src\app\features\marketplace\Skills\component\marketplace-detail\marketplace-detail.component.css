/* Conteneur principal */
div {
    max-width: 800px;
    margin: 40px auto;
    padding: 20px;
    font-family: 'Segoe UI', sans-serif;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  /* Titre */
  h2 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    color: #333;
  }
  
  /* Tableau */
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 1rem;
    background-color: #fafafa;
    border-radius: 8px;
    overflow: hidden;
  }
  
  /* En-têtes du tableau */
  th {
    background-color: #343a40;
    color: white;
    text-align: left;
    padding: 12px 16px;
  }
  
  /* Cellules */
  td {
    padding: 12px 16px;
    border-top: 1px solid #dee2e6;
    color: black;
  }
  
  /* Lignes alternées pour plus de lisibilité */
  tr:nth-child(even) td {
    background-color: #f1f3f5;
  }
  
  /* Liste dans les compétences validées */
  td ul {
    margin: 0;
    padding-left: 20px;
  }
  
  td ul li {
    list-style-type: disc;
  }
  
  /* Texte de chargement */
  p {
    text-align: center;
    font-style: italic;
    color: #6c757d;
  }
  