// Script pour tester les endpoints quiz
const https = require('http');

const BASE_URL = 'http://127.0.0.1:3000';

const endpoints = [
  '/api/quiz/categories-with-challenges',
  '/api/quiz/leaderboard',
  '/api/quiz/leaderboard/category/68037cda1603b8c8bad94668',
  '/api/test'
];

function testEndpoint(endpoint) {
  return new Promise((resolve) => {
    const url = `${BASE_URL}${endpoint}`;
    
    const req = https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          endpoint,
          status: res.statusCode,
          success: res.statusCode === 200,
          data: data.length > 0 ? JSON.parse(data) : null
        });
      });
    });
    
    req.on('error', (error) => {
      resolve({
        endpoint,
        status: 'ERROR',
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        endpoint,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timeout'
      });
    });
  });
}

async function testAllEndpoints() {
  console.log('🧪 Test des endpoints Quiz...\n');
  
  for (const endpoint of endpoints) {
    try {
      const result = await testEndpoint(endpoint);
      
      if (result.success) {
        console.log(`✅ ${endpoint} - Status: ${result.status}`);
        if (result.data && Array.isArray(result.data)) {
          console.log(`   📊 Données: ${result.data.length} éléments`);
        } else if (result.data && result.data.data && Array.isArray(result.data.data)) {
          console.log(`   📊 Données: ${result.data.data.length} éléments`);
        }
      } else {
        console.log(`❌ ${endpoint} - Status: ${result.status}`);
        if (result.error) {
          console.log(`   🔍 Erreur: ${result.error}`);
        }
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - Erreur: ${error.message}`);
    }
    
    console.log(''); // Ligne vide
  }
  
  console.log('📋 Résumé:');
  console.log('- Si categories-with-challenges ✅ : Votre serveur fonctionne');
  console.log('- Si leaderboard ❌ : Il faut ajouter les routes quiz');
  console.log('- Consultez AJOUT_ROUTES_SERVEUR_EXISTANT.md pour la solution');
}

// Exécuter les tests
testAllEndpoints().catch(console.error);
