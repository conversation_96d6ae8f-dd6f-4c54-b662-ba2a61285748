<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-white mb-4">
        🎯 Sélectionnez une Catégorie de Quiz
      </h1>
      <p class="text-xl text-gray-300 mb-6">
        Choisissez votre domaine d'expertise et commencez à gagner des points !
      </p>

      <!-- Boutons d'action -->
      <div class="flex justify-center space-x-4 mb-6">
        <button
          (click)="goToProfile()"
          class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
          👤 Mon Profil
        </button>
        <button
          (click)="goToLeaderboard()"
          class="px-6 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
          🏆 Classement
        </button>
        <button
          (click)="refreshCategories()"
          class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          🔄 Actualiser
        </button>
      </div>
    </div>

    <!-- Debug info -->
    <div class="bg-blue-500 text-white p-4 rounded-lg mb-6 text-center" *ngIf="!loading">
      <p><strong>Debug Info:</strong></p>
      <p>Loading: {{ loading }}</p>
      <p>Categories length: {{ categories.length }}</p>
      <p>Error message: {{ errorMessage }}</p>
      <p *ngIf="categories.length > 0">First category: {{ categories[0].name }}</p>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="errorMessage" class="bg-red-500 text-white p-4 rounded-lg mb-6 text-center">
      {{ errorMessage }}
    </div>

    <!-- Loading -->
    <div *ngIf="loading" class="text-center">
      <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      <p class="text-white mt-4">Chargement des catégories...</p>
    </div>

    <!-- Grille des catégories -->
    <div *ngIf="!loading && categories.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        *ngFor="let category of categories"
        class="bg-white/10 backdrop-blur-md rounded-xl p-6 hover:bg-white/20 transition-all duration-300 cursor-pointer transform hover:scale-105"
        (click)="selectCategory(category)">

        <!-- Icône et titre -->
        <div class="text-center mb-4">
          <div class="text-6xl mb-3">{{ getCategoryIcon(category.name) }}</div>
          <h3 class="text-2xl font-bold text-white mb-2">{{ category.name }}</h3>
          <p class="text-gray-300 text-sm">{{ category.description }}</p>
        </div>

        <!-- Statistiques -->
        <div class="space-y-3">
          <!-- Nombre de challenges -->
          <div class="flex justify-between items-center">
            <span class="text-gray-300">Challenges disponibles:</span>
            <span class="text-white font-semibold">{{ category.totalChallenges }}</span>
          </div>

          <!-- Challenges complétés -->
          <div class="flex justify-between items-center" *ngIf="category.completedChallenges !== undefined">
            <span class="text-gray-300">Complétés:</span>
            <span class="text-green-400 font-semibold">{{ category.completedChallenges || 0 }}</span>
          </div>

          <!-- Barre de progression -->
          <div class="w-full bg-gray-700 rounded-full h-2">
            <div
              class="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-300"
              [style.width.%]="getCategoryProgress(category)">
            </div>
          </div>
          <div class="text-center text-sm text-gray-300">
            {{ getCategoryProgress(category) }}% complété
          </div>

          <!-- Score moyen -->
          <div class="flex justify-between items-center" *ngIf="category.averageScore !== undefined">
            <span class="text-gray-300">Score moyen:</span>
            <span [class]="getDifficultyColor(category.averageScore)" class="font-semibold">
              {{ category.averageScore || 0 }}%
            </span>
          </div>

          <!-- Niveau de difficulté -->
          <div class="flex justify-between items-center">
            <span class="text-gray-300">Difficulté:</span>
            <span [class]="getDifficultyColor(category.averageScore)" class="font-semibold">
              {{ getDifficultyText(category.averageScore) }}
            </span>
          </div>
        </div>

        <!-- Bouton d'action -->
        <div class="mt-6 text-center">
          <button
            class="w-full py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-300"
            [disabled]="category.totalChallenges === 0">
            <span *ngIf="category.totalChallenges > 0">🚀 Commencer les Quiz</span>
            <span *ngIf="category.totalChallenges === 0">❌ Aucun challenge</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Message si aucune catégorie -->
    <div *ngIf="!loading && categories.length === 0" class="text-center text-white">
      <div class="text-6xl mb-4">📚</div>
      <h3 class="text-2xl font-bold mb-4">Aucune catégorie disponible</h3>
      <p class="text-gray-300 mb-6">Les catégories de quiz seront bientôt disponibles.</p>
      <button
        (click)="refreshCategories()"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
        🔄 Réessayer
      </button>
    </div>

    <!-- Information sur le système de points -->
    <div class="mt-12 bg-white/10 backdrop-blur-md rounded-xl p-6">
      <h3 class="text-2xl font-bold text-white mb-4 text-center">🎖️ Système de Récompenses</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">🥉</div>
          <h4 class="text-white font-semibold">Participant</h4>
          <p class="text-gray-300 text-sm">0-49%</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">🥈</div>
          <h4 class="text-white font-semibold">Débutant</h4>
          <p class="text-gray-300 text-sm">50-69%</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">🥇</div>
          <h4 class="text-white font-semibold">Intermédiaire</h4>
          <p class="text-gray-300 text-sm">70-89%</p>
        </div>
        <div class="bg-white/10 rounded-lg p-4">
          <div class="text-3xl mb-2">💎</div>
          <h4 class="text-white font-semibold">Expert</h4>
          <p class="text-gray-300 text-sm">90-100%</p>
        </div>
      </div>
      <div class="text-center mt-6">
        <p class="text-yellow-300 font-semibold">🏆 Atteignez 1000 points pour obtenir votre certificat ! 🏆</p>
      </div>
    </div>
  </div>
</div>
