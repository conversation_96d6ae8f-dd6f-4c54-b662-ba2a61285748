<div class="container mt-4">
  <h2 class="text-center mb-4 text-violet-dark">🌟 Liste des Badges</h2>

  <!-- Boutons Ajouter et Leaderboard -->
  <div class="d-flex justify-content-between mb-3">
    <button class="btn btn-success" [routerLink]="['/badges/create']">
      ➕ Ajouter un Badge
    </button>
    <button class="btn btn-warning" [routerLink]="['/badges/leaderboard']">
      🏆 Voir le Classement
    </button> 
  </div>

  <!-- Filtre par type de badge -->
  <div class="mb-3">
    <label for="typeFilter" class="form-label text-violet-dark">🎯 Filtrer par type :</label>
    <select id="typeFilter" class="form-select custom-select" [(ngModel)]="searchType">
      <option value="">Tous</option>
      <option value="PARTICIPANT">Participant</option>
      <option value="DEBUTANT">Débutant</option>
      <option value="INTERMIDIAIRE">Intermédiaire</option>
      <option value="EXPERT">Expert</option>
    </select>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="errorMessage" class="alert alert-danger">
    {{ errorMessage }}
  </div>

  <!-- Liste des badges -->
  <div *ngIf="badges && badges.length > 0">
    <div class="row">
      <div *ngFor="let badge of filteredBadges()" class="col-md-6 col-lg-4 mb-4">
        <div class="card badge-card">
          <div class="card-body">
            <h4 class="badge-title">
              {{ badge.name }}
              <span class="badge-type">{{ badge.type }}</span>
            </h4>
            <p class="card-text"><strong>Pourcentage :</strong> {{ badge.percentage }}%</p>
            <p class="card-text"><strong>Date d'attribution :</strong> {{ badge.awardedAt | date: 'short' }}</p>

            <!-- Certificat -->
            <div *ngIf="badge.certificateImageUrl" class="mt-3 certificate-badge">
              <p><strong>📜 Certificat Obtenu !</strong></p>
              <a
                class="btn btn-outline-secondary btn-sm"
                [href]="badge.certificateImageUrl"
                target="_blank"
              >
                Voir Certificat
              </a>
            </div>

            <div class="d-grid gap-2 mt-3">
              <button class="btn btn-primary btn-sm" [routerLink]="['/badges/user', currentUser]">
                🎖 Voir les badges
              </button>
              <button class="btn btn-danger btn-sm" (click)="deleteBadge(badge._id)">
                🗑 Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message si aucun badge -->
  <div *ngIf="badges && filteredBadges().length === 0" class="alert alert-info text-center text-light">
    Aucun badge trouvé pour ce filtre.
  </div>
</div>