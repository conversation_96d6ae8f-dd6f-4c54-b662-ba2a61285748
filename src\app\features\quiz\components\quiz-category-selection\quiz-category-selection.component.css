/* Animations personnalisées */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.category-card {
  animation: fadeInUp 0.6s ease-out;
}

.category-card:hover {
  animation: pulse 2s infinite;
}

/* Effet de brillance sur les cartes */
.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.category-card:hover::before {
  left: 100%;
}

/* Styles pour les badges de difficulté */
.difficulty-easy {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.difficulty-medium {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.difficulty-hard {
  background: linear-gradient(135deg, #ef4444, #f87171);
}

/* Effet de survol sur les boutons */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.btn-hover-effect:hover::before {
  left: 100%;
}

/* Styles pour la barre de progression */
.progress-bar {
  background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6);
  background-size: 200% 100%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Effet de particules en arrière-plan */
.particles-bg {
  position: relative;
  overflow: hidden;
}

.particles-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Styles responsifs */
@media (max-width: 768px) {
  .category-card {
    margin-bottom: 1rem;
  }
  
  .grid-responsive {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .text-responsive {
    font-size: 1.5rem;
  }
  
  .p-responsive {
    padding: 1rem;
  }
}

/* Effet de glow pour les éléments importants */
.glow-effect {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(139, 92, 246, 0.8);
}

/* Styles pour les icônes animées */
.icon-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}
