const mongoose = require('mongoose');

const BadgeSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['PARTICIPANT', 'BEGINNER', 'INTERMEDIATE', 'EXPERT', 'MASTER'],
    required: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  imageUrl: {
    type: String
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  },
  challengeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Challenge'
  },
  scoreRequired: {
    type: Number,
    default: 0
  },
  percentageRequired: {
    type: Number,
    default: 0
  },
  awardedAt: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
});

// Index pour optimiser les requêtes
BadgeSchema.index({ userId: 1, awardedAt: -1 });
BadgeSchema.index({ type: 1 });
BadgeSchema.index({ categoryId: 1 });

// Méthodes statiques
BadgeSchema.statics.getUserBadges = function(userId) {
  return this.find({ userId: userId, isActive: true })
    .populate('categoryId')
    .populate('challengeId')
    .sort({ awardedAt: -1 });
};

BadgeSchema.statics.getBadgesByCategory = function(userId, categoryId) {
  return this.find({ 
    userId: userId, 
    categoryId: categoryId, 
    isActive: true 
  }).sort({ awardedAt: -1 });
};

BadgeSchema.statics.getLeaderboard = function(limit = 10) {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: '$userId',
        totalBadges: { $sum: 1 },
        expertBadges: {
          $sum: { $cond: [{ $eq: ['$type', 'EXPERT'] }, 1, 0] }
        },
        intermediateBadges: {
          $sum: { $cond: [{ $eq: ['$type', 'INTERMEDIATE'] }, 1, 0] }
        },
        beginnerBadges: {
          $sum: { $cond: [{ $eq: ['$type', 'BEGINNER'] }, 1, 0] }
        },
        lastBadgeDate: { $max: '$awardedAt' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $addFields: {
        userName: '$user.fullName',
        score: {
          $add: [
            { $multiply: ['$expertBadges', 100] },
            { $multiply: ['$intermediateBadges', 50] },
            { $multiply: ['$beginnerBadges', 25] }
          ]
        }
      }
    },
    {
      $sort: { score: -1, lastBadgeDate: -1 }
    },
    {
      $limit: limit
    },
    {
      $project: {
        userId: '$_id',
        userName: 1,
        totalBadges: 1,
        expertBadges: 1,
        intermediateBadges: 1,
        beginnerBadges: 1,
        score: 1,
        lastBadgeDate: 1
      }
    }
  ]);
};

module.exports = mongoose.model('Badge', BadgeSchema);
