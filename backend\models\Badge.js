const mongoose = require('mongoose');
const badgeImageGenerator = require('../services/badgeImageGenerator');

const BadgeSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['PARTICIPANT', 'BEGINNER', 'INTERMEDIATE', 'EXPERT', 'MASTER'],
    required: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  imageUrl: {
    type: String
  },
  certificateImageUrl: {
    type: String
  },
  percentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  quizScore: {
    type: Number,
    default: 0
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  },
  challengeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Challenge'
  },
  scoreRequired: {
    type: Number,
    default: 0
  },
  percentageRequired: {
    type: Number,
    default: 0
  },
  awardedAt: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  }
});

// Middleware pour générer automatiquement les images
BadgeSchema.pre('save', async function(next) {
  if (this.isNew || this.isModified('percentage') || this.isModified('type')) {
    try {
      // Populate les références nécessaires
      await this.populate(['userId', 'categoryId']);

      const userName = this.userId?.fullName || this.userId?.name || 'Utilisateur';
      const categoryName = this.categoryId?.name || 'Général';

      // Générer l'image du badge
      this.imageUrl = badgeImageGenerator.generateCustomBadgeSVG(
        this.type,
        userName,
        categoryName,
        this.percentage
      );

      // Générer le certificat si le score total atteint 1000
      if (this.quizScore >= 1000 && !this.certificateImageUrl) {
        this.certificateImageUrl = badgeImageGenerator.generateCustomCertificateSVG(
          userName,
          this.quizScore,
          { category: categoryName, percentage: this.percentage }
        );
      }
    } catch (error) {
      console.error('Erreur lors de la génération des images:', error);
    }
  }
  next();
});

// Index pour optimiser les requêtes
BadgeSchema.index({ userId: 1, awardedAt: -1 });
BadgeSchema.index({ type: 1 });
BadgeSchema.index({ categoryId: 1 });

// Méthodes statiques
BadgeSchema.statics.getUserBadges = function(userId) {
  return this.find({ userId: userId, isActive: true })
    .populate('categoryId')
    .populate('challengeId')
    .sort({ awardedAt: -1 });
};

BadgeSchema.statics.getBadgesByCategory = function(userId, categoryId) {
  return this.find({
    userId: userId,
    categoryId: categoryId,
    isActive: true
  }).sort({ awardedAt: -1 });
};

BadgeSchema.statics.getLeaderboard = function(limit = 10) {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: '$userId',
        totalBadges: { $sum: 1 },
        expertBadges: {
          $sum: { $cond: [{ $eq: ['$type', 'EXPERT'] }, 1, 0] }
        },
        intermediateBadges: {
          $sum: { $cond: [{ $eq: ['$type', 'INTERMEDIATE'] }, 1, 0] }
        },
        beginnerBadges: {
          $sum: { $cond: [{ $eq: ['$type', 'BEGINNER'] }, 1, 0] }
        },
        lastBadgeDate: { $max: '$awardedAt' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $addFields: {
        userName: '$user.fullName',
        score: {
          $add: [
            { $multiply: ['$expertBadges', 100] },
            { $multiply: ['$intermediateBadges', 50] },
            { $multiply: ['$beginnerBadges', 25] }
          ]
        }
      }
    },
    {
      $sort: { score: -1, lastBadgeDate: -1 }
    },
    {
      $limit: limit
    },
    {
      $project: {
        userId: '$_id',
        userName: 1,
        totalBadges: 1,
        expertBadges: 1,
        intermediateBadges: 1,
        beginnerBadges: 1,
        score: 1,
        lastBadgeDate: 1
      }
    }
  ]);
};

// Méthode pour créer automatiquement un badge après un quiz
BadgeSchema.statics.createBadgeFromQuiz = async function(quizResult) {
  const { userId, challengeId, categoryId, percentage, score } = quizResult;

  try {
    // Déterminer le type de badge basé sur le pourcentage
    const badgeType = badgeImageGenerator.getBadgeType(percentage);

    // Vérifier si l'utilisateur a déjà un badge pour ce challenge
    const existingBadge = await this.findOne({
      userId: userId,
      challengeId: challengeId,
      isActive: true
    });

    if (existingBadge) {
      // Mettre à jour le badge existant si le nouveau score est meilleur
      if (percentage > existingBadge.percentage) {
        existingBadge.percentage = percentage;
        existingBadge.type = badgeType;
        existingBadge.quizScore = score;
        existingBadge.awardedAt = new Date();
        await existingBadge.save();
        return existingBadge;
      }
      return existingBadge;
    }

    // Créer un nouveau badge
    const badge = new this({
      userId: userId,
      challengeId: challengeId,
      categoryId: categoryId,
      type: badgeType,
      name: `Badge ${badgeType}`,
      description: `Badge obtenu avec ${percentage}% de réussite`,
      percentage: percentage,
      quizScore: score,
      scoreRequired: 0,
      percentageRequired: 50
    });

    await badge.save();
    return badge;
  } catch (error) {
    console.error('Erreur lors de la création du badge:', error);
    throw error;
  }
};

// Méthode pour supprimer automatiquement les badges quand un challenge est supprimé
BadgeSchema.statics.deleteBadgesForChallenge = async function(challengeId) {
  try {
    const result = await this.updateMany(
      { challengeId: challengeId },
      { isActive: false }
    );
    console.log(`${result.modifiedCount} badges désactivés pour le challenge ${challengeId}`);
    return result;
  } catch (error) {
    console.error('Erreur lors de la suppression des badges:', error);
    throw error;
  }
};

module.exports = mongoose.model('Badge', BadgeSchema);
