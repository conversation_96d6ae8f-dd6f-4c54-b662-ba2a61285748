export interface Feedback {
    _id?: string; // ID du feedback (généré automatiquement par MongoDB)
    userId: string; // ID de l'utilisateur qui a donné le feedback
    targetUserId: string; // ID de l'utilisateur cible
    salonId: string; // ID du salon où le feedback a été donné
    rating: number; // Note (entre 1 et 5)
    comment?: string; // Commentaire optionnel
    sessionStartTime: Date; // Heure de début de la session
    feedbackTriggeredAt?: Date; // Quand le feedback a été déclenché
    isActive?: boolean; // Si le feedback est actif
    createdAt?: Date; // Date de création du feedback
    updatedAt?: Date; // Date de mise à jour

    // Données populées
    user?: {
      _id: string;
      fullName: string;
      email: string;
    };
    targetUser?: {
      _id: string;
      fullName: string;
      email: string;
    };
    salon?: {
      _id: string;
      nom: string;
      description: string;
    };
  }

  export interface PersonalityTraits {
    openness: number; // Ouverture
    conscientiousness: number; // Conscience professionnelle
    extraversion: number; // Extraversion
    agreeableness: number; // Agréabilité
    neuroticism: number; // Névrosisme
  }