const mongoose = require('mongoose');

const SalonSchema = new mongoose.Schema({
  nom: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  createurId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  participants: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    joinedAt: {
      type: Date,
      default: Date.now
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  sessionStartTime: {
    type: Date
  },
  sessionEndTime: {
    type: Date
  },
  isSessionActive: {
    type: Boolean,
    default: false
  },
  maxParticipants: {
    type: Number,
    default: 10
  },
  isActive: {
    type: Boolean,
    default: true
  },
  dateCreation: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index pour optimiser les requêtes
SalonSchema.index({ createurId: 1 });
SalonSchema.index({ 'participants.userId': 1 });
SalonSchema.index({ isSessionActive: 1 });
SalonSchema.index({ dateCreation: -1 });

// Middleware pour mettre à jour updatedAt
SalonSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Méthodes d'instance
SalonSchema.methods.startSession = function() {
  this.sessionStartTime = new Date();
  this.isSessionActive = true;
  return this.save();
};

SalonSchema.methods.endSession = function() {
  this.sessionEndTime = new Date();
  this.isSessionActive = false;
  return this.save();
};

SalonSchema.methods.addParticipant = function(userId) {
  const existingParticipant = this.participants.find(p => 
    p.userId.toString() === userId.toString() && p.isActive
  );
  
  if (!existingParticipant && this.participants.length < this.maxParticipants) {
    this.participants.push({
      userId: userId,
      joinedAt: new Date(),
      isActive: true
    });
    return this.save();
  }
  
  return Promise.resolve(this);
};

SalonSchema.methods.removeParticipant = function(userId) {
  const participant = this.participants.find(p => 
    p.userId.toString() === userId.toString()
  );
  
  if (participant) {
    participant.isActive = false;
  }
  
  return this.save();
};

SalonSchema.methods.getActiveParticipants = function() {
  return this.participants.filter(p => p.isActive);
};

// Méthodes statiques
SalonSchema.statics.getActiveSalons = function() {
  return this.find({ isActive: true })
    .populate('createurId', 'fullName email')
    .populate('participants.userId', 'fullName email')
    .sort({ dateCreation: -1 });
};

SalonSchema.statics.getSalonsForUser = function(userId) {
  return this.find({
    $or: [
      { createurId: userId },
      { 'participants.userId': userId, 'participants.isActive': true }
    ],
    isActive: true
  })
    .populate('createurId', 'fullName email')
    .populate('participants.userId', 'fullName email')
    .sort({ dateCreation: -1 });
};

SalonSchema.statics.getActiveSessions = function() {
  return this.find({ isSessionActive: true })
    .populate('createurId', 'fullName email')
    .populate('participants.userId', 'fullName email');
};

// Méthode pour déclencher automatiquement les feedbacks après 5 minutes
SalonSchema.methods.checkAndTriggerFeedbacks = async function() {
  if (!this.isSessionActive || !this.sessionStartTime) {
    return null;
  }
  
  const fiveMinutesAgo = new Date(this.sessionStartTime.getTime() + 5 * 60 * 1000);
  const now = new Date();
  
  if (now >= fiveMinutesAgo) {
    const Feedback = require('./Feedback');
    const activeParticipants = this.getActiveParticipants();
    
    if (activeParticipants.length >= 2) {
      return await Feedback.triggerFeedbacksForSession(
        this._id,
        activeParticipants,
        this.sessionStartTime
      );
    }
  }
  
  return null;
};

module.exports = mongoose.model('Salon', SalonSchema);
