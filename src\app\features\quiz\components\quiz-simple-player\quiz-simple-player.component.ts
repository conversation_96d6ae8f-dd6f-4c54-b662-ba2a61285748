import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { QuizService } from '../../services/quiz.service';

@Component({
  selector: 'app-quiz-simple-player',
  template: `
    <div class="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
      <div class="max-w-4xl mx-auto">

        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="text-4xl font-bold text-white mb-2">🎯 Quiz Player</h1>
          <p class="text-gray-300" *ngIf="challenge">{{ challenge.title }}</p>
        </div>

        <!-- Loading -->
        <div *ngIf="loading" class="text-center py-12">
          <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto"></div>
          <p class="text-white mt-4">Chargement du quiz...</p>
        </div>

        <!-- Error -->
        <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500 text-red-100 p-6 rounded-lg text-center">
          <h3 class="text-xl font-bold mb-2">❌ Erreur</h3>
          <p>{{ errorMessage }}</p>
          <button (click)="goBack()" class="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
            Retour
          </button>
        </div>

        <!-- Quiz Content -->
        <div *ngIf="!loading && !errorMessage && challenge">

          <!-- Progress Bar -->
          <div class="bg-white/10 backdrop-blur-md rounded-xl p-6 mb-6">
            <div class="flex justify-between items-center mb-2">
              <span class="text-white">Question {{ currentQuestionIndex + 1 }} sur {{ challenge.questions.length }}</span>
              <span class="text-white">{{ timeRemaining }}s</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                   [style.width.%]="(currentQuestionIndex + 1) / challenge.questions.length * 100"></div>
            </div>
          </div>

          <!-- Question -->
          <div class="bg-white/10 backdrop-blur-md rounded-xl p-8 mb-6" *ngIf="currentQuestion">
            <h2 class="text-2xl font-bold text-white mb-6">{{ currentQuestion.question }}</h2>

            <!-- Answers -->
            <div class="space-y-4">
              <button *ngFor="let answer of currentQuestion.allAnswers; let i = index"
                      (click)="selectAnswer(answer)"
                      [class]="getAnswerClass(answer)"
                      [disabled]="selectedAnswer !== null"
                      class="w-full p-4 text-left rounded-lg border-2 transition-all duration-200 hover:scale-105">
                <span class="font-medium">{{ getAnswerLetter(i) }}.</span>
                {{ answer }}
              </button>
            </div>
          </div>

          <!-- Navigation -->
          <div class="flex justify-between items-center">
            <button (click)="previousQuestion()"
                    [disabled]="currentQuestionIndex === 0"
                    class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed">
              ← Précédent
            </button>

            <div class="text-center">
              <p class="text-white text-sm">Score actuel: {{ score }}/{{ (currentQuestionIndex + 1) * 10 }}</p>
            </div>

            <button (click)="nextQuestion()"
                    [disabled]="selectedAnswer === null"
                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
              {{ currentQuestionIndex === challenge.questions.length - 1 ? 'Terminer' : 'Suivant' }} →
            </button>
          </div>
        </div>

        <!-- Results -->
        <div *ngIf="showResults" class="bg-white/10 backdrop-blur-md rounded-xl p-8 text-center">
          <h2 class="text-3xl font-bold text-white mb-4">🎉 Quiz Terminé !</h2>
          <div class="text-6xl mb-4">{{ getResultEmoji() }}</div>
          <p class="text-xl text-white mb-2">Score Final: {{ score }}/{{ challenge.questions.length * 10 }}</p>
          <p class="text-lg text-gray-300 mb-6">Pourcentage: {{ getPercentage() }}%</p>

          <div class="flex justify-center space-x-4">
            <button (click)="restartQuiz()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700">
              🔄 Recommencer
            </button>
            <button (click)="goBack()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              📋 Retour aux Quiz
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .animate-spin {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .answer-default {
      @apply bg-white/5 border-white/20 text-white hover:bg-white/10;
    }

    .answer-selected {
      @apply bg-blue-500/30 border-blue-400 text-white;
    }

    .answer-correct {
      @apply bg-green-500/30 border-green-400 text-white;
    }

    .answer-incorrect {
      @apply bg-red-500/30 border-red-400 text-white;
    }
  `]
})
export class QuizSimplePlayerComponent implements OnInit {
  challenge: any = null;
  currentQuestionIndex = 0;
  selectedAnswer: string | null = null;
  answers: string[] = [];
  score = 0;
  loading = true;
  errorMessage: string | null = null;
  showResults = false;
  timeRemaining = 30;
  timer: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private quizService: QuizService
  ) {}

  ngOnInit(): void {
    const challengeId = this.route.snapshot.paramMap.get('id');
    if (challengeId) {
      this.loadChallenge(challengeId);
    } else {
      this.errorMessage = 'ID du challenge manquant';
      this.loading = false;
    }
  }

  loadChallenge(challengeId: string): void {
    // Simuler le chargement d'un challenge avec des données de test
    setTimeout(() => {
      this.challenge = {
        _id: challengeId,
        title: 'Quiz JavaScript Basics',
        description: 'Testez vos connaissances en JavaScript',
        questions: [
          {
            _id: '1',
            question: 'Que signifie "JS" ?',
            correct_answer: 'JavaScript',
            incorrect_answers: ['Java Source', 'JSON Script', 'Just Script'],
            allAnswers: ['JavaScript', 'Java Source', 'JSON Script', 'Just Script']
          },
          {
            _id: '2',
            question: 'Quel est le type de données pour stocker du texte en JavaScript ?',
            correct_answer: 'string',
            incorrect_answers: ['text', 'char', 'varchar'],
            allAnswers: ['string', 'text', 'char', 'varchar']
          },
          {
            _id: '3',
            question: 'Comment déclarer une variable en JavaScript ?',
            correct_answer: 'var myVar',
            incorrect_answers: ['variable myVar', 'declare myVar', 'dim myVar'],
            allAnswers: ['var myVar', 'variable myVar', 'declare myVar', 'dim myVar']
          }
        ]
      };

      // Mélanger les réponses pour chaque question
      this.challenge.questions.forEach((q: any) => {
        q.allAnswers = this.shuffleArray([q.correct_answer, ...q.incorrect_answers]);
      });

      this.answers = new Array(this.challenge.questions.length).fill('');
      this.loading = false;
      this.startTimer();
    }, 1000);
  }

  get currentQuestion() {
    return this.challenge?.questions[this.currentQuestionIndex];
  }

  selectAnswer(answer: string): void {
    if (this.selectedAnswer !== null) return;

    this.selectedAnswer = answer;
    this.answers[this.currentQuestionIndex] = answer;

    // Vérifier si la réponse est correcte
    if (answer === this.currentQuestion.correct_answer) {
      this.score += 10;
    }

    // Auto-advance après 2 secondes
    setTimeout(() => {
      this.nextQuestion();
    }, 2000);
  }

  nextQuestion(): void {
    if (this.currentQuestionIndex < this.challenge.questions.length - 1) {
      this.currentQuestionIndex++;
      this.selectedAnswer = null;
      this.resetTimer();
    } else {
      this.finishQuiz();
    }
  }

  previousQuestion(): void {
    if (this.currentQuestionIndex > 0) {
      this.currentQuestionIndex--;
      this.selectedAnswer = this.answers[this.currentQuestionIndex] || null;
      this.resetTimer();
    }
  }

  finishQuiz(): void {
    this.showResults = true;
    this.clearTimer();
  }

  restartQuiz(): void {
    this.currentQuestionIndex = 0;
    this.selectedAnswer = null;
    this.answers = new Array(this.challenge.questions.length).fill('');
    this.score = 0;
    this.showResults = false;
    this.startTimer();
  }

  goBack(): void {
    this.router.navigate(['/quiz/challenges']);
  }

  getAnswerClass(answer: string): string {
    if (this.selectedAnswer === null) {
      return 'answer-default';
    }

    if (answer === this.selectedAnswer) {
      return answer === this.currentQuestion.correct_answer ? 'answer-correct' : 'answer-incorrect';
    }

    if (answer === this.currentQuestion.correct_answer) {
      return 'answer-correct';
    }

    return 'answer-default';
  }

  getPercentage(): number {
    return Math.round((this.score / (this.challenge.questions.length * 10)) * 100);
  }

  getResultEmoji(): string {
    const percentage = this.getPercentage();
    if (percentage >= 90) return '🏆';
    if (percentage >= 70) return '🥈';
    if (percentage >= 50) return '🥉';
    return '📚';
  }

  startTimer(): void {
    this.timeRemaining = 30;
    this.timer = setInterval(() => {
      this.timeRemaining--;
      if (this.timeRemaining <= 0) {
        this.nextQuestion();
      }
    }, 1000);
  }

  resetTimer(): void {
    this.clearTimer();
    this.startTimer();
  }

  clearTimer(): void {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  shuffleArray(array: any[]): any[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  getAnswerLetter(index: number): string {
    return String.fromCharCode(65 + index);
  }

  ngOnDestroy(): void {
    this.clearTimer();
  }
}
