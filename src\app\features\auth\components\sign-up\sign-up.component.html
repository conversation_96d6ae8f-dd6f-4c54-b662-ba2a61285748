<div class="flex items-center justify-center min-h-screen">
  <form
    [formGroup]="signUpForm"
    (ngSubmit)="onSubmit()"
    class="sign-up-form grid-form"
  >
    <h2 class="text-2xl font-semibold text-center mb-6 text-black">Sign Up</h2>
    <div *ngIf="error" class="error">{{ error }}</div>
    <div *ngIf="success" class="success">{{ success }}</div>
    <div class="form-columns">
      <div>
        <!-- Left column -->
        <label for="fullName" class="block text-gray-700 mb-1">Full Name</label>
        <input
          id="fullName"
          formControlName="fullName"
          type="text"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none text-black focus:ring-2 focus:ring-cyan-400"
        />
        <div
          *ngIf="
            signUpForm.get('fullName')?.touched &&
            signUpForm.get('fullName')?.invalid
          "
          class="validation"
        >
          Full name is required.
        </div>

        <label for="password" class="block text-gray-700 mb-1 mt-4"
          >Password</label
        >
        <input
          id="password"
          formControlName="password"
          type="password"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none text-black focus:ring-2 focus:ring-cyan-400"
        />
        <div
          *ngIf="
            signUpForm.get('password')?.touched &&
            signUpForm.get('password')?.invalid
          "
          class="validation"
        >
          <span *ngIf="signUpForm.get('password')?.errors?.['required']"
            >Password is required.</span
          >
          <span *ngIf="signUpForm.get('password')?.errors?.['minlength']"
            >Password must be at least 6 characters.</span
          >
        </div>
      </div>
      <div>
        <!-- Right column -->
        <label for="email" class="block text-gray-700 mb-1">Email</label>
        <input
          id="email"
          formControlName="email"
          type="email"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none text-black focus:ring-2 focus:ring-cyan-400"
        />
        <div
          *ngIf="
            signUpForm.get('email')?.touched && signUpForm.get('email')?.invalid
          "
          class="validation"
        >
          <span
            *ngIf="signUpForm.get('email')?.errors?.['required']"
            class="text-red-500 text-sm mt-1"
            >Email is required.</span
          >
          <span *ngIf="signUpForm.get('email')?.errors?.['email']"
            >Invalid email format.</span
          >
        </div>

        <label for="confirmPassword" class="block text-gray-700 mb-1 mt-4"
          >Confirm Password</label
        >
        <input
          id="confirmPassword"
          formControlName="confirmPassword"
          type="password"
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none text-black focus:ring-2 focus:ring-cyan-400"
        />
        <div
          *ngIf="
            signUpForm.get('confirmPassword')?.touched &&
            signUpForm.get('confirmPassword')?.invalid
          "
          class="validation"
        >
          <span *ngIf="signUpForm.get('confirmPassword')?.errors?.['required']">
            Confirm password is required.
          </span>
          <span
            *ngIf="signUpForm.get('confirmPassword')?.errors?.['passwordMismatch']"
          >
            Passwords do not match.
          </span>
        </div>
      </div>
    </div>
    <div class="skills-row">
      <div>
        <label for="skills" class="block text-gray-700 mb-1">Skills</label>
        <input
          id="skills"
          type="text"
          (input)="onSkillInput($event)"
          [disabled]="loading"
          placeholder="Type to search skills..."
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none text-black focus:ring-2 focus:ring-cyan-400"
        />
        <div *ngIf="skills$ | async as skills">
          <ul
            *ngIf="skills.length > 0"
            class="bg-white border border-gray-200 rounded shadow mt-1 max-h-40 overflow-auto"
          >
            <li
              *ngFor="let skill of skills"
              (click)="selectSkill(skill)"
              class="px-4 py-2 cursor-pointer hover:bg-cyan-100 text-black"
            >
              {{ skill.name }}
            </li>
          </ul>
        </div>
        <div class="mt-2 flex flex-wrap gap-2">
          <span
            *ngFor="let skill of selectedSkills"
            class="bg-green-200 text-cyan-900 px-2 py-1 rounded flex items-center"
          >
            {{ skill.name }}
            <button
              type="button"
              (click)="removeSkill(skill)"
              class="ml-2 text-red-500 hover:text-red-700"
            >
              &times;
            </button>
          </span>
        </div>
      </div>
    </div>
    <button type="submit" [disabled]="loading || signUpForm.invalid">
      Sign Up
    </button>
  </form>
</div>
