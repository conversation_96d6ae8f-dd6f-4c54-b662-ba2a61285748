<div
  class="max-w-2xl h-[50%] mx-auto mt-16 p-8 rounded-2xl shadow-lg gradient-bg"
>
  <h2 class="text-2xl font-semibold text-center mb-6 text-white bg-[]">
    Sign In
  </h2>

  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col justify-around w-full h-[90%]"
  >
    <div class="mb-4">
      <label class="block text-white mb-1">Email</label>
      <input
        formControlName="email"
        type="email"
        placeholder="<EMAIL>"
        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none text-black focus:ring-2 focus:ring-cyan-400"
      />
      <p
        *ngIf="form.controls.email.invalid && form.controls.email.touched"
        class="text-red-500 text-sm mt-1"
      >
        Valid email required.
      </p>
    </div>

    <div class="mb-6">
      <label class="block text-white mb-1">Password</label>
      <input
        formControlName="password"
        type="password"
        placeholder="••••••••"
        class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 text-black focus:ring-cyan-400"
      />
      <p
        *ngIf="form.controls.password.invalid && form.controls.password.touched"
        class="text-red-500 text-sm mt-1"
      >
        Password is required.
      </p>
    </div>

    <button
      type="submit"
      [disabled]="isLoading || form.invalid"
      class="w-full py-3 font-bold rounded-lg text-white bg-cyan-500 hover:bg-cyan-600 disabled:opacity-50"
    >
      {{ isLoading ? "Signing In…" : "Sign In" }}
    </button>

    <p *ngIf="errorMsg" class="text-red-500 text-center mt-4">{{ errorMsg }}</p>
  </form>

  <p class="text-center text-white mt-6">
    Don't have an account?
    <a routerLink="/signUp" class="text-cyan-500 hover:underline">Sign Up</a>
  </p>
</div>
