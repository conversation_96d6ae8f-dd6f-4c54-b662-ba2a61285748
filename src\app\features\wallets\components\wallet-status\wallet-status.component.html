<div class="mt-4">
  <!-- <button 
    (click)="toggleWalletStatus()" 
    [disabled]="isLoading"
    [ngClass]="wallet?.isActive 
      ? 'bg-red-600 hover:bg-red-700' 
      : 'bg-green-600 hover:bg-green-700'"
    class="w-full text-white py-2 px-4 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed"
  > -->
  <button 
    [disabled]="isLoading"
    [ngClass]="wallet?.isActive 
      ? 'bg-red-600 hover:bg-red-700' 
      : 'bg-green-600 hover:bg-green-700'"
    class="w-full text-white py-2 px-4 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed"
  >
    <span *ngIf="isLoading" class="inline-block mr-2">
      <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </span>
    {{ wallet?.isActive ? 'Deactivate Wallet' : 'Activate Wallet' }}
  </button>
  
  <div *ngIf="errorMessage" class="mt-2 text-red-500 text-sm">
    {{ errorMessage }}
  </div>
  
  <div *ngIf="successMessage" class="mt-2 text-green-500 text-sm">
    {{ successMessage }}
  </div>
  
  <div *ngIf="!wallet?.isActive" class="mt-2 text-amber-500 text-sm">
    Note: You can only activate a wallet after 48 hours of deactivation.
  </div>
</div>