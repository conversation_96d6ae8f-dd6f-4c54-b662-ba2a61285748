import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QuizCategorySelectionComponent } from './components/quiz-category-selection/quiz-category-selection.component';
import { QuizChallengeSelectionComponent } from './components/quiz-challenge-selection/quiz-challenge-selection.component';
import { QuizByCategoryComponent } from './components/quiz-by-category/quiz-by-category.component';
import { QuizPlayerComponent } from './components/quiz-player/quiz-player.component';
import { QuizResultsComponent } from './components/quiz-results/quiz-results.component';
import { QuizProfileComponent } from './components/quiz-profile/quiz-profile.component';
import { QuizLeaderboardComponent } from './components/quiz-leaderboard/quiz-leaderboard.component';
import { BadgeDisplayComponent } from './components/badge-display/badge-display.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'challenges',
    pathMatch: 'full'
  },
  {
    path: 'challenges',
    component: QuizChallengeSelectionComponent,
    data: { title: 'Sélection de Challenge' }
  },
  {
    path: 'categories',
    component: QuizCategorySelectionComponent,
    data: { title: 'Sélection de Catégorie' }
  },
  {
    path: 'category/:id',
    component: QuizByCategoryComponent,
    data: { title: 'Quiz par Catégorie' }
  },
  {
    path: 'play/:id',
    component: QuizPlayerComponent,
    data: { title: 'Jouer au Quiz' }
  },
  {
    path: 'results',
    component: QuizResultsComponent,
    data: { title: 'Résultats du Quiz' }
  },
  {
    path: 'quiz-profile',
    component: QuizProfileComponent,
    data: { title: 'Profil Quiz' }
  },
  {
    path: 'leaderboard',
    component: QuizLeaderboardComponent,
    data: { title: 'Classement Quiz' }
  },
  {
    path: 'badges',
    component: BadgeDisplayComponent,
    data: { title: 'Mes Badges' }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class QuizRoutingModule { }
