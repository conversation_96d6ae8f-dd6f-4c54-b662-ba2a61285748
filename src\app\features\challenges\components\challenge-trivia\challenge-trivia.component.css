body {
    background-color: #121212;
    color: #e0e0e0;
  }
  
  .container {
    background-color: #1e1e2f;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.7);
  }
  
  h1, h2 {
    color: #90caf9;
    text-shadow: 1px 1px 5px #000;
  }
  
  .form-label {
    color: #f5f5f5;
  }
  
  .form-control, .form-select {
    background-color: #2c2c3a;
    border: 1px solid #444;
    color: #fff;
  }
  
  .form-control:focus, .form-select:focus {
    border-color: #90caf9;
    box-shadow: 0 0 10px #90caf9;
  }
  
  .btn-primary {
    background-color: #1976d2;
    border: none;
    box-shadow: 0 5px 15px rgba(25, 118, 210, 0.4);
  }
  
  .btn-primary:hover {
    background-color: #1565c0;
  }
  
  .alert {
    border-radius: 10px;
  }
  
  ul {
    padding-left: 0;
  }
  
  li {
    list-style: none;
    background: #2c2c3a;
    margin: 10px 0;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
  }
  
  li:hover {
    transform: translateY(-5px);
    background-color: #3a3a4f;
  }
  
  h4 {
    color: #ffca28;
  }
  