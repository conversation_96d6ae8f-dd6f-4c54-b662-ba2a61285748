# 🎯 Système de Quiz avec Catégories, Scores et Badges

Ce module implémente un système complet de quiz avec catégories, système de scoring, attribution de badges et certification.

## 🚀 Fonctionnalités

### ✨ Fonctionnalités principales
- **Sélection de catégories** : Interface pour choisir une catégorie de quiz
- **Quiz par catégorie** : Affichage des challenges disponibles par catégorie
- **Lecteur de quiz interactif** : Interface de jeu avec timer, navigation entre questions
- **Système de scoring** : Points basés sur la difficulté (Facile: 10pts, Moyen: 15pts, Difficile: 20pts)
- **Attribution automatique de badges** : Basée sur le pourcentage de réussite
- **Certification** : Certificat automatique à 1000 points
- **Profil utilisateur** : Statistiques personnelles et historique
- **Classement** : Leaderboard global et par catégorie

### 🏆 Système de Badges
- **PARTICIPANT** : 0-49% de réussite
- **BEGINNER** : 50-69% de réussite  
- **INTERMEDIATE** : 70-89% de réussite
- **EXPERT** : 90-100% de réussite

### 🎓 Système de Certification
- Certificat automatique à 1000 points
- Téléchargement PDF du certificat
- Suivi de la progression vers la certification

## 📁 Structure des Fichiers

```
src/app/features/quiz/
├── components/
│   ├── quiz-category-selection/     # Sélection de catégorie
│   ├── quiz-by-category/           # Quiz par catégorie
│   ├── quiz-player/                # Lecteur de quiz
│   ├── quiz-results/               # Résultats détaillés
│   ├── quiz-profile/               # Profil utilisateur
│   └── quiz-leaderboard/           # Classement
├── models/
│   └── quiz.models.ts              # Modèles TypeScript
├── services/
│   └── quiz.service.ts             # Service principal
├── quiz.module.ts                  # Module Angular
├── quiz-routing.module.ts          # Routes
└── README.md                       # Documentation
```

## 🛠️ Services Étendus

### ChallengeService (étendu)
```typescript
// Nouvelles méthodes ajoutées
getChallengesByCategory(categoryId: string)
getCategoriesWithChallenges()
getLocalCategories()
createChallengeWithCategory(challengeData)
getCategoryStats(categoryId: string)
```

### BadgeService (étendu)
```typescript
// Nouvelles méthodes ajoutées
assignBadgeByScore(userId, score, challengeId, categoryId)
getUserTotalScore(userId)
checkCertificateEligibility(userId)
awardCertificate(userId)
getUserCertificates(userId)
getBadgesByCategory(userId, categoryId)
getLeaderboardByCategory(categoryId)
generateCertificatePDF(userId, certificateData)
```

### QuizService (nouveau)
```typescript
// Service principal pour le système de quiz
getCategoriesWithChallenges()
getChallengesByCategory(categoryId)
startQuizSession(challengeId, userId)
submitQuiz(challengeId, userId, answers)
saveQuizScore(scoreData)
getUserProgress(userId)
checkCertificateEligibility(userId)
getLeaderboard(limit)
getCategoryLeaderboard(categoryId, limit)
```

## 🎮 Flux Utilisateur

1. **Sélection de catégorie** (`/quiz/categories`)
   - Affichage des catégories avec statistiques
   - Progression vers le certificat
   - Navigation vers les quiz

2. **Choix du challenge** (`/quiz/category/:id`)
   - Liste des challenges par catégorie
   - Filtres par difficulté
   - Recherche de challenges

3. **Jeu du quiz** (`/quiz/play/:id`)
   - Interface interactive avec timer
   - Navigation entre questions
   - Sauvegarde automatique des réponses

4. **Résultats** (`/quiz/results`)
   - Score détaillé avec pourcentage
   - Attribution de badge
   - Vérification de certification
   - Analyse des réponses correctes/incorrectes

5. **Profil** (`/quiz/profile`)
   - Statistiques personnelles
   - Historique des scores
   - Badges gagnés
   - Progression vers la certification

6. **Classement** (`/quiz/leaderboard`)
   - Leaderboard global et par catégorie
   - Podium des 3 premiers
   - Position de l'utilisateur

## 🔧 Configuration Backend Requise

### Endpoints API nécessaires

```typescript
// Challenges avec catégories
GET /api/challenges/categories-with-challenges
GET /api/challenges/category/:categoryId
GET /api/challenges/:challengeId

// Quiz et scores
POST /api/quiz/submit
POST /api/quiz/scores
GET /api/quiz/scores/user/:userId
GET /api/quiz/progress/:userId
PUT /api/quiz/progress/:userId

// Badges et certification
POST /api/badges/assign-by-score
GET /api/badges/user/:userId/certificate-eligibility
POST /api/badges/award-certificate
GET /api/badges/user/:userId/certificates
POST /api/badges/generate-certificate-pdf

// Classements
GET /api/quiz/leaderboard
GET /api/quiz/leaderboard/category/:categoryId
```

### Modèles de données MongoDB

```javascript
// QuizScore
{
  userId: ObjectId,
  challengeId: ObjectId,
  categoryId: ObjectId,
  score: Number,
  totalQuestions: Number,
  percentage: Number,
  timeSpent: Number,
  completedAt: Date
}

// UserProgress
{
  userId: ObjectId,
  totalScore: Number,
  totalQuizzes: Number,
  averagePercentage: Number,
  categoriesCompleted: [ObjectId],
  badgesEarned: [ObjectId],
  certificateEarned: Boolean,
  certificateEarnedAt: Date
}

// Challenge (étendu)
{
  title: String,
  description: String,
  categoryId: ObjectId,
  difficulty: String,
  questions: [QuizQuestion],
  isActive: Boolean,
  timeLimit: Number
}
```

## 🎨 Styles et Animations

- **Animations CSS** : Transitions fluides, effets de survol
- **Design responsive** : Compatible mobile et desktop
- **Thème sombre** : Interface moderne avec dégradés
- **Icônes émojis** : Interface ludique et intuitive
- **Effets visuels** : Confettis, brillances, pulsations

## 🚀 Utilisation

### Installation
```bash
# Le module est déjà intégré dans l'application
# Aucune installation supplémentaire requise
```

### Navigation
```typescript
// Accès aux quiz
this.router.navigate(['/quiz/categories']);

// Quiz par catégorie
this.router.navigate(['/quiz/category', categoryId]);

// Jouer un quiz
this.router.navigate(['/quiz/play', challengeId]);

// Profil utilisateur
this.router.navigate(['/quiz/profile']);

// Classement
this.router.navigate(['/quiz/leaderboard']);
```

### Intégration dans d'autres composants
```typescript
import { QuizService } from './features/quiz/services/quiz.service';

// Vérifier l'éligibilité au certificat
this.quizService.checkCertificateEligibility(userId).subscribe(result => {
  if (result.eligible) {
    // Utilisateur éligible pour le certificat
  }
});

// Obtenir le score total
this.quizService.getUserProgress(userId).subscribe(progress => {
  console.log('Score total:', progress.totalScore);
});
```

## 🔮 Fonctionnalités Futures

- **Quiz en temps réel multijoueur**
- **Défis entre utilisateurs**
- **Système de récompenses avancé**
- **Intégration avec l'IA pour questions personnalisées**
- **Statistiques avancées avec graphiques**
- **Système de notifications push**
- **Mode hors ligne**
- **Partage sur réseaux sociaux**

## 🤝 Contribution

Pour contribuer au système de quiz :

1. Respecter la structure des modèles existants
2. Ajouter des tests unitaires pour les nouveaux composants
3. Maintenir la cohérence du design
4. Documenter les nouvelles fonctionnalités
5. Tester la compatibilité mobile

## 📞 Support

Pour toute question ou problème :
- Vérifier la console pour les erreurs API
- S'assurer que le backend est configuré correctement
- Vérifier les permissions utilisateur
- Consulter la documentation des services
