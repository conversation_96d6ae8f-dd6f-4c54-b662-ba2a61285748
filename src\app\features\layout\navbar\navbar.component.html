<nav class="navbar">
  <div class="navbar-logo">
    <img src="assets/logo_transparent.png" alt="Logo" width="60 px" />
  </div>
  <ul class="navbar-links">
    <li><a routerLink="/">Home</a></li>
    <li><a routerLink="/MarketplaceList">Marketplace</a></li>
    <li><a routerLink="/skill/add">Ajouter une Compétence</a></li>
    <li><a routerLink="/quiz/categories">🎯 Quiz</a></li>
    <li><a routerLink="/profil">Chat</a></li>
    <li><a routerLink="/profil">Notre Communaute</a></li>
    <li><a href="/logIn">Log In</a></li>
  </ul>
  <button class="navbar-toggle" (click)="toggleMenu()">☰</button>
</nav>

<ul class="navbar-mobile" *ngIf="isMenuOpen">
  <li><a routerLink="/" (click)="toggleMenu()">Accueil</a></li>
  <li><a routerLink="/MarketplaceList" (click)="toggleMenu()">Marketplace</a></li>
  <li><a routerLink="/skill/add" (click)="toggleMenu()">Ajouter Skill</a></li>
  <li><a routerLink="/quiz/categories" (click)="toggleMenu()">🎯 Quiz</a></li>
  <li><a routerLink="/profil" (click)="toggleMenu()">Profil</a></li>
  <li><a href="/logIn">Log In</a></li>
</ul>
