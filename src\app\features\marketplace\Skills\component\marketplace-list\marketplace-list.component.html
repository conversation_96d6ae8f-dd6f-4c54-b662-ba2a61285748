<div >

     <h1 class="marketplace-title">Marketplace</h1>
  
    
<form class="max-w-lg mx-auto" (submit)="searchSkills(); $event.preventDefault()">
    <div class="flex">
        <label for="search-dropdown" class="mb-2 text-sm font-medium text-gray-900 sr-only dark:text-white">Rechercher</label>
        <select id="category-select" [(ngModel)]="selectedCategory" name="category" class="shrink-0 z-10 inline-flex items-center py-2.5 px-4 text-sm font-medium text-center text-gray-900 bg-gray-100 border border-gray-300 rounded-s-lg hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100">
            <option value="">Toutes catégories</option>
            <option *ngFor="let cat of categories" [value]="cat.name">{{ cat.name }}</option>
        </select>
        
        <div class="relative w-full">
            <input type="search" id="search-dropdown" [(ngModel)]="searchText" name="searchText" class="block p-2.5 w-full z-20 text-sm text-gray-900 bg-gray-50 rounded-e-lg border-s-gray-50 border-s-2 border border-gray-300 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher des compétences..." />
            <button type="submit" class="absolute top-0 end-0 p-2.5 text-sm font-medium h-full text-white bg-blue-700 rounded-e-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300">
                <svg class="w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                </svg>
                <span class="sr-only">Search</span>
            </button>
        </div>
    </div>
</form>

  

  <div *ngIf="skills.length > 0; else noSkills" class="skill-list">
    <div class="skill-card" *ngFor="let skill of skills">
      <h3>{{ skill.name }}</h3>
      <p><strong>Description :</strong> {{ skill.description }}</p>
      <p><strong>Catégorie :</strong> {{ skill.category?.name }}</p>
      <p><strong>Tuteur :</strong> {{ getUserInfo(skill) }}</p>
      <p><strong>GitHub Username :</strong> {{ skill.github?.username || 'N/A' }}</p>

      <div>
        <strong>Compétences GitHub Validées :</strong>
        <ul>
          <li *ngFor="let s of skill.github?.validatedSkills || []">{{ s }}</li>
        </ul>
      </div>

      <p><strong>Dernière mise à jour GitHub :</strong> {{ skill.github?.lastUpdated | date:'short' }}</p>
      <p><strong>Créé le :</strong> {{ skill.createdAt | date:'short' }}</p>
      <p><strong>Modifié le :</strong> {{ skill.updatedAt | date:'short' }}</p>

      <div class="skill-card-buttons">
        <button (click)="onViewDetail(skill)">Détail</button>
        <button (click)="onEditSkill(skill)">Update</button>
        <button (click)="onDeleteSkill(skill)">Delete</button>
      </div>
      <div>
        <button (click)="goToSalon(skill)" class="nav-button salon-button">Voir les salons</button>
      </div>
      <div>
        <button (click)="goToSession(skill)" class="nav-button session-button">Voir les sessions</button>
      </div>
    </div>
  </div>



  <ng-template #noSkills>
    <p>Aucune compétence trouvée.</p>
  </ng-template>

</div>



