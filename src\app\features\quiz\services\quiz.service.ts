import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { 
  QuizScore, 
  UserProgress, 
  QuizChallenge, 
  CategoryWithChallenges, 
  QuizResult,
  QuizSession 
} from '../models/quiz.models';

@Injectable({
  providedIn: 'root'
})
export class QuizService {
  private apiUrl = 'http://127.0.0.1:3000/api';
  private currentSessionSubject = new BehaviorSubject<QuizSession | null>(null);
  public currentSession$ = this.currentSessionSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Récupérer toutes les catégories avec leurs challenges
  getCategoriesWithChallenges(): Observable<CategoryWithChallenges[]> {
    return this.http.get<CategoryWithChallenges[]>(`${this.apiUrl}/quiz/categories-with-challenges`);
  }

  // Récupérer les challenges par catégorie
  getChallengesByCategory(categoryId: string): Observable<QuizChallenge[]> {
    return this.http.get<QuizChallenge[]>(`${this.apiUrl}/quiz/challenges/category/${categoryId}`);
  }

  // Récupérer un challenge spécifique avec ses questions
  getChallengeById(challengeId: string): Observable<QuizChallenge> {
    return this.http.get<QuizChallenge>(`${this.apiUrl}/challenges/${challengeId}`);
  }

  // Démarrer une session de quiz
  startQuizSession(challengeId: string, userId: string): Observable<QuizSession> {
    const sessionData = {
      challengeId,
      userId,
      startTime: new Date(),
      currentQuestionIndex: 0,
      answers: {},
      isCompleted: false
    };

    this.currentSessionSubject.next(sessionData);
    return new Observable(observer => {
      observer.next(sessionData);
      observer.complete();
    });
  }

  // Sauvegarder une réponse
  saveAnswer(questionId: string, answer: string): void {
    const currentSession = this.currentSessionSubject.value;
    if (currentSession) {
      currentSession.answers[questionId] = answer;
      this.currentSessionSubject.next(currentSession);
    }
  }

  // Passer à la question suivante
  nextQuestion(): void {
    const currentSession = this.currentSessionSubject.value;
    if (currentSession) {
      currentSession.currentQuestionIndex++;
      this.currentSessionSubject.next(currentSession);
    }
  }

  // Soumettre le quiz et calculer le score
  submitQuiz(challengeId: string, userId: string, answers: { [questionId: string]: string }): Observable<QuizResult> {
    const submitData = {
      challengeId,
      userId,
      answers,
      completedAt: new Date()
    };

    return this.http.post<QuizResult>(`${this.apiUrl}/quiz/submit`, submitData).pipe(
      tap(() => {
        // Réinitialiser la session
        this.currentSessionSubject.next(null);
      })
    );
  }

  // Sauvegarder le score dans la base de données
  saveQuizScore(scoreData: Partial<QuizScore>): Observable<QuizScore> {
    return this.http.post<QuizScore>(`${this.apiUrl}/quiz/scores`, scoreData);
  }

  // Récupérer les scores d'un utilisateur
  getUserScores(userId: string, categoryId?: string): Observable<QuizScore[]> {
    let params = new HttpParams();
    if (categoryId) {
      params = params.set('categoryId', categoryId);
    }
    
    return this.http.get<QuizScore[]>(`${this.apiUrl}/quiz/scores/user/${userId}`, { params });
  }

  // Récupérer la progression d'un utilisateur
  getUserProgress(userId: string): Observable<UserProgress> {
    return this.http.get<UserProgress>(`${this.apiUrl}/quiz/progress/${userId}`);
  }

  // Mettre à jour la progression d'un utilisateur
  updateUserProgress(userId: string, scoreData: QuizScore): Observable<UserProgress> {
    return this.http.put<UserProgress>(`${this.apiUrl}/quiz/progress/${userId}`, scoreData);
  }

  // Vérifier si l'utilisateur mérite un certificat
  checkCertificateEligibility(userId: string): Observable<{ eligible: boolean; totalScore: number }> {
    return this.http.get<{ eligible: boolean; totalScore: number }>(`${this.apiUrl}/quiz/certificate-check/${userId}`);
  }

  // Attribuer un certificat
  awardCertificate(userId: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/quiz/award-certificate`, { userId });
  }

  // Récupérer le classement général
  getLeaderboard(limit: number = 10): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/quiz/leaderboard?limit=${limit}`);
  }

  // Récupérer le classement par catégorie
  getCategoryLeaderboard(categoryId: string, limit: number = 10): Observable<any[]> {
    return this.http.get<any[]>(`${this.apiUrl}/quiz/leaderboard/category/${categoryId}?limit=${limit}`);
  }

  // Récupérer les statistiques d'un utilisateur
  getUserStats(userId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}/quiz/stats/${userId}`);
  }

  // Méthodes utilitaires pour la session locale
  getCurrentSession(): QuizSession | null {
    return this.currentSessionSubject.value;
  }

  clearSession(): void {
    this.currentSessionSubject.next(null);
  }

  // Calculer le pourcentage de réussite
  calculatePercentage(correctAnswers: number, totalQuestions: number): number {
    return Math.round((correctAnswers / totalQuestions) * 100);
  }

  // Déterminer le type de badge basé sur le pourcentage
  getBadgeType(percentage: number): string {
    if (percentage >= 90) return 'EXPERT';
    if (percentage >= 70) return 'INTERMEDIATE';
    if (percentage >= 50) return 'BEGINNER';
    return 'PARTICIPANT';
  }
}
