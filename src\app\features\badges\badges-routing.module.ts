import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BadgeListComponent } from './components/badge-list/badge-list.component';
import { BadgeFormComponent } from './components/badge-form/badge-form.component';
import { BadgeDetailComponent } from './components/badge-detail/badge-detail.component';
import { LeaderboardComponent } from './components/leaderboard/leaderboard.component';
import { BadgeSimpleComponent } from './components/badge-simple/badge-simple.component';

const routes: Routes = [
  { path: '', component: BadgeSimpleComponent },
  { path: 'list', component: BadgeListComponent },
  { path: 'create', component: BadgeFormComponent },
  { path: 'user/:userId', component: BadgeDetailComponent },
  { path: 'leaderboard', component: LeaderboardComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BadgesRoutingModule { }
